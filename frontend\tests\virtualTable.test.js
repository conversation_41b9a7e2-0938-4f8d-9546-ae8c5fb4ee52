// DOC: docs/micro-tools/search-bulk-feature.md
/**
 * @jest-environment jsdom
 */
const VirtualTable = require('../../static/js/modules/micro-tools/components/VirtualTable.js');

describe('VirtualTable', () => {
  beforeEach(() => {
    document.body.innerHTML = '<div id="container"></div>';
  });

  test('setData correctly processes large datasets and updates content height', () => {
    const columns = [{ key: 'id', name: 'ID' }];
    const vt = new VirtualTable({ containerId: 'container', columns, rowHeight: 20 });
    vt.scrollElem = document.createElement('div');
    Object.defineProperty(vt.scrollElem, 'clientHeight', { value: 200 });
    vt.contentElem = document.createElement('div');
    vt.onResize();
    const data = Array.from({ length: 10000 }, (_, i) => ({ id: i }));
    vt.setData(data);
    expect(vt.filtered.length).toBe(10000);
    expect(vt.contentElem.style.height).toBe(`${10000 * 20}px`);
  });

  test('applyFilter filters data based on filter input', () => {
    const columns = [{ key: 'name', name: 'Name' }];
    const vt = new VirtualTable({ containerId: 'container', columns, rowHeight: 10 });
    vt.scrollElem = document.createElement('div');
    Object.defineProperty(vt.scrollElem, 'clientHeight', { value: 100 });
    vt.contentElem = document.createElement('div');
    vt.filterInput = document.createElement('input');
    vt.filterInput.value = 'al';
    vt.data = [
      { name: 'Alice' },
      { name: 'Bob' },
      { name: 'Charlie' },
    ];
    vt.applyFilter();
    expect(vt.filtered).toEqual([{ name: 'Alice' }]);
  });

  test('sortBy sorts data ascending and descending', () => {
    const columns = [{ key: 'value', name: 'Value' }];
    const vt = new VirtualTable({ containerId: 'container', columns, rowHeight: 10 });
    vt.scrollElem = document.createElement('div');
    Object.defineProperty(vt.scrollElem, 'clientHeight', { value: 100 });
    vt.contentElem = document.createElement('div');
    vt.filterInput = document.createElement('input');
    vt.data = [
      { value: 3 },
      { value: 1 },
      { value: 2 },
    ];
    vt.applyFilter();
    vt.sortBy('value');
    expect(vt.filtered.map(x => x.value)).toEqual([1, 2, 3]);
    vt.sortBy('value');
    expect(vt.filtered.map(x => x.value)).toEqual([3, 2, 1]);
  });
});