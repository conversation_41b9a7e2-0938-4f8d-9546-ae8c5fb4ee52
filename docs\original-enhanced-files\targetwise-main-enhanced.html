<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise - Algorithmic Targeting Sheet Builder</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f0f2f5;
            color: #1a1a2e;
            min-height: 100vh;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .logo:hover {
            transform: translateY(-1px);
        }

        .logo svg {
            width: 32px;
            height: 32px;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .nav-link {
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .nav-link:hover {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.08);
        }

        .nav-link.active {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .theme-toggle {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: none;
            background: #f1f5f9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: #e2e8f0;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 48px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
        }

        .feature-icon {
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Background decoration */
        .hero-decoration {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
        }

        .circle-1 {
            width: 400px;
            height: 400px;
            top: -200px;
            right: -100px;
        }

        .circle-2 {
            width: 300px;
            height: 300px;
            bottom: -150px;
            left: -50px;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: -60px auto 60px;
            padding: 0 24px;
            position: relative;
            z-index: 10;
        }

        /* Builder Card */
        .builder-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
            padding: 48px;
            margin-bottom: 40px;
        }

        .builder-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .builder-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
        }

        .builder-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .builder-description {
            color: #64748b;
            font-size: 16px;
        }

        /* Form Sections */
        .form-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            background: #f0f9ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2563eb;
        }

        /* File Upload */
        .file-upload-area {
            border: 2px dashed #e2e8f0;
            border-radius: 12px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            background: #fafbfc;
        }

        .file-upload-area:hover {
            border-color: #2563eb;
            background: #f0f9ff;
        }

        .file-upload-area.active {
            border-color: #2563eb;
            background: #f0f9ff;
            border-style: solid;
        }

        .upload-icon {
            width: 48px;
            height: 48px;
            background: #e0f2fe;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: #2563eb;
        }

        .upload-text {
            color: #475569;
            margin-bottom: 12px;
        }

        .upload-hint {
            color: #94a3b8;
            font-size: 14px;
        }

        .file-input {
            display: none;
        }

        .sample-csv-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
            margin-top: 16px;
            transition: all 0.2s;
        }

        .sample-csv-link:hover {
            gap: 12px;
            color: #1d4ed8;
        }

        /* Text Input */
        .textarea-wrapper {
            position: relative;
        }

        .form-textarea {
            width: 100%;
            min-height: 120px;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-family: inherit;
            font-size: 16px;
            resize: vertical;
            transition: all 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .char-counter {
            position: absolute;
            bottom: 12px;
            right: 12px;
            color: #94a3b8;
            font-size: 13px;
        }

        /* Input Grid */
        .input-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
        }

        .form-input {
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Age Range */
        .age-range {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 16px;
            align-items: end;
        }

        .range-separator {
            color: #94a3b8;
            padding-bottom: 12px;
        }

        /* Submit Button */
        .submit-section {
            text-align: center;
            margin-top: 48px;
        }

        .btn-submit {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
            padding: 16px 48px;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
            display: inline-flex;
            align-items: center;
            gap: 12px;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(37, 99, 235, 0.35);
        }

        .btn-submit:active {
            transform: translateY(0);
        }

        /* Info Cards */
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .info-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s;
        }

        .info-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }

        .info-card-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .info-card:nth-child(2) .info-card-icon {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .info-card:nth-child(3) .info-card-icon {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }

        .info-card-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .info-card-text {
            color: #64748b;
            line-height: 1.6;
        }

        /* Loading State */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .spinner {
            width: 48px;
            height: 48px;
            border: 3px solid #f3f4f6;
            border-top-color: #2563eb;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .loading-subtext {
            color: #64748b;
        }

        /* Success Message */
        .success-message {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
            display: none;
            align-items: center;
            gap: 12px;
        }

        .success-icon {
            width: 24px;
            height: 24px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .success-text {
            color: #059669;
            font-weight: 500;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 36px;
            }
            
            .hero-subtitle {
                font-size: 18px;
            }
            
            .builder-card {
                padding: 32px 24px;
            }
            
            .builder-title {
                font-size: 24px;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .age-range {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .range-separator {
                display: none;
            }
        }

        @media (max-width: 640px) {
            .nav-menu {
                display: none;
            }
            
            .hero-section {
                padding: 60px 0;
            }
            
            .info-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <circle cx="12" cy="12" r="6"></circle>
                    <circle cx="12" cy="12" r="2"></circle>
                </svg>
                TargetWise
            </a>
            
            <nav class="nav-menu">
                <a href="#" class="nav-link active">Home</a>
                <a href="#" class="nav-link">Micro-Tools</a>
                <a href="#" class="nav-link">Pricing</a>
                <a href="#" class="nav-link">Docs</a>
            </nav>
            
            <div class="header-actions">
                <button class="theme-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">Welcome to TargetWise</h1>
            <p class="hero-subtitle">Build powerful 12-column Algorithmic Targeting Sheets using the Meta Marketing API for optimized Facebook ad campaigns</p>
            <div class="hero-features">
                <div class="hero-feature">
                    <div class="feature-icon">✓</div>
                    <span>AI-Powered Targeting</span>
                </div>
                <div class="hero-feature">
                    <div class="feature-icon">✓</div>
                    <span>Meta API Integration</span>
                </div>
                <div class="hero-feature">
                    <div class="feature-icon">✓</div>
                    <span>Export Ready</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Info Cards -->
        <div class="info-cards">
            <div class="info-card">
                <div class="info-card-icon">🎯</div>
                <h3 class="info-card-title">Smart Targeting</h3>
                <p class="info-card-text">Generate precise audience segments using advanced algorithms and Meta's comprehensive interest database</p>
            </div>
            <div class="info-card">
                <div class="info-card-icon">📊</div>
                <h3 class="info-card-title">12-Column Format</h3>
                <p class="info-card-text">Industry-standard targeting sheet format optimized for Facebook Ads Manager import and campaign setup</p>
            </div>
            <div class="info-card">
                <div class="info-card-icon">⚡</div>
                <h3 class="info-card-title">Instant Results</h3>
                <p class="info-card-text">Get your targeting sheet in seconds, ready to use in your Facebook advertising campaigns</p>
            </div>
        </div>

        <!-- Builder Card -->
        <div class="builder-card">
            <div class="builder-header">
                <h2 class="builder-title">
                    <div class="builder-icon">🚀</div>
                    Build Your Targeting Sheet
                </h2>
                <p class="builder-description">Upload your interests or enter them manually to generate a comprehensive targeting strategy</p>
            </div>

            <div class="success-message" id="successMessage">
                <div class="success-icon">✓</div>
                <span class="success-text">Targeting sheet generated successfully!</span>
            </div>

            <form id="targetingForm">
                <!-- Upload Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <div class="section-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                        </div>
                        Upload Interests CSV (Optional)
                    </h3>
                    <div class="file-upload-area" id="fileUploadArea">
                        <input type="file" id="fileInput" class="file-input" accept=".csv">
                        <div class="upload-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14 2 14 8 20 8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10 9 9 9 8 9"></polyline>
                            </svg>
                        </div>
                        <p class="upload-text">Click to upload or drag and drop</p>
                        <p class="upload-hint">CSV files only (max 10MB)</p>
                    </div>
                    <a href="#" class="sample-csv-link">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Download Sample CSV
                    </a>
                </div>

                <!-- Manual Input Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <div class="section-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                        </div>
                        Seed Interests (Optional if CSV uploaded)
                    </h3>
                    <div class="textarea-wrapper">
                        <textarea 
                            class="form-textarea" 
                            placeholder="Enter comma-separated interests (e.g., fitness, yoga, meditation, wellness, health)..."
                            id="seedInterests"
                        ></textarea>
                        <span class="char-counter">0 interests</span>
                    </div>
                </div>

                <!-- Settings Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <div class="section-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"></circle>
                                <path d="M12 1v6m0 6v6m-9-9h6m6 0h6m-11.364 6.364l4.243-4.243m0 0l4.242-4.242m-4.242 4.242l-4.243-4.243m4.243 4.243l4.242 4.242"></path>
                            </svg>
                        </div>
                        Targeting Parameters
                    </h3>
                    <div class="input-grid">
                        <div class="form-group">
                            <label class="form-label">Country Code</label>
                            <input type="text" class="form-input" value="US" placeholder="e.g., US" id="countryCode">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Age Range</label>
                            <div class="age-range">
                                <input type="number" class="form-input" value="18" placeholder="Min" min="13" max="65" id="minAge">
                                <span class="range-separator">to</span>
                                <input type="number" class="form-input" value="65" placeholder="Max" min="13" max="65" id="maxAge">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="submit-section">
                    <button type="submit" class="btn-submit">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 2L11 13"></path>
                            <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                        </svg>
                        Build Targeting Sheet
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <p class="loading-text">Building Your Targeting Sheet</p>
            <p class="loading-subtext">This may take a few seconds...</p>
        </div>
    </div>

    <script>
        // File upload functionality
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');
        const seedInterests = document.getElementById('seedInterests');
        const charCounter = document.querySelector('.char-counter');

        fileUploadArea.addEventListener('click', () => fileInput.click());

        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('active');
        });

        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('active');
        });

        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('active');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                const uploadText = fileUploadArea.querySelector('.upload-text');
                uploadText.textContent = `Selected: ${file.name}`;
                fileUploadArea.classList.add('active');
            }
        }

        // Interest counter
        seedInterests.addEventListener('input', () => {
            const interests = seedInterests.value.split(',').filter(i => i.trim()).length;
            charCounter.textContent = `${interests} interests`;
        });

        // Form submission
        document.getElementById('targetingForm').addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Validation
            const hasFile = fileInput.files.length > 0;
            const hasInterests = seedInterests.value.trim().length > 0;
            
            if (!hasFile && !hasInterests) {
                alert('Please upload a CSV file or enter seed interests');
                return;
            }
            
            // Show loading
            document.getElementById('loadingOverlay').style.display = 'flex';
            
            // Simulate API call
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';
                document.getElementById('successMessage').style.display = 'flex';
                
                // Reset form
                setTimeout(() => {
                    document.getElementById('successMessage').style.display = 'none';
                }, 5000);
            }, 3000);
        });

        // Sample CSV download
        document.querySelector('.sample-csv-link').addEventListener('click', (e) => {
            e.preventDefault();
            // In real app, this would download a file
            alert('Sample CSV download started');
        });
    </script>
</body>
</html>