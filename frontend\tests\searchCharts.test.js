// DOC: docs/micro-tools/search-bulk-feature.md
/**
 * @jest-environment jsdom
 */

describe('SearchCharts', () => {
  let SearchCharts;
  let originalGetComputedStyle;
  let mockChartInstances;

  beforeEach(() => {
    document.body.innerHTML = `
      <canvas id="audienceChart"></canvas>
      <canvas id="categoryChart"></canvas>
    `;
    require('../../static/js/modules/micro-tools/components/SearchCharts.js');
    SearchCharts = window.SearchCharts;
    originalGetComputedStyle = window.getComputedStyle;
    window.getComputedStyle = () => ({ getPropertyValue: () => '' });
    mockChartInstances = [];
    global.Chart = jest.fn((ctx, config) => {
      const chart = { options: config.options, update: jest.fn(), destroy: jest.fn() };
      mockChartInstances.push({ ctx, config, chart });
      return chart;
    });
  });

  afterEach(() => {
    window.getComputedStyle = originalGetComputedStyle;
    delete global.Chart;
    mockChartInstances = [];
  });

  test('getColors returns fallback defaults when CSS variables are not set', () => {
    const sc = new SearchCharts();
    const colors = sc.getColors();
    expect(colors.text).toBe('#212529');
    expect(colors.grid).toBe('rgba(0,0,0,0.1)');
    expect(colors.backgrounds).toEqual([
      '#0066ff',
      '#28a745',
      '#ffc107',
      '#dc3545',
      '#6610f2',
    ]);
  });

  test('render processes data and creates correct chart configurations', () => {
    const sc = new SearchCharts();
    const results = [
      { audience_size_lower_bound: 5000, audience_size_upper_bound: 8000, type: 'A' },
      { audience_size_lower_bound: 20000, audience_size_upper_bound: 30000, type: 'B' },
      { audience_size_lower_bound: 500000, audience_size_upper_bound: 600000 },
      { audience_size_lower_bound: 2000000, audience_size_upper_bound: 2500000, type: 'A' },
    ];
    sc.render(results);
    expect(global.Chart).toHaveBeenCalledTimes(2);
    const audienceConfig = mockChartInstances[0].config;
    expect(audienceConfig.type).toBe('bar');
    expect(audienceConfig.data.labels).toEqual(['0-10k', '10k-100k', '100k-1M', '1M+']);
    expect(audienceConfig.data.datasets[0].data).toEqual([1, 1, 1, 1]);
    const categoryConfig = mockChartInstances[1].config;
    expect(categoryConfig.type).toBe('pie');
    expect(categoryConfig.data.labels.sort()).toEqual(['A', 'B', 'interest'].sort());
    expect(categoryConfig.data.datasets[0].data.sort()).toEqual([2, 1, 1].sort());
  });
});