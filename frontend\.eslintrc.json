{"env": {"browser": true, "es2021": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"no-unused-vars": ["warn"], "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "warn", "no-var": "error", "eqeqeq": ["error", "always", {"null": "ignore"}]}, "ignorePatterns": ["node_modules/", "dist/", "static/js/", "coverage/"]}