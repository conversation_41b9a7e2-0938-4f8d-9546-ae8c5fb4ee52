/**
 * Interest Pool Page - Vanilla JavaScript Implementation
 * Manages collected interests with filtering and export functionality
 */

export function InterestPoolPage() {
    return `
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="/" class="logo">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    TargetWise
                </a>

                <nav class="nav-menu">
                    <a href="/" class="nav-link">Home</a>
                    <a href="/dashboard" class="nav-link">Dashboard</a>
                    <a href="/search" class="nav-link">Search</a>
                    <a href="/suggestions" class="nav-link">Suggestions</a>
                    <a href="/pool" class="nav-link active">Interest Pool</a>
                </nav>

                <div class="header-actions">
                    <button class="theme-toggle">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Container -->
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <h3 class="sidebar-title">Micro-Tools</h3>
                <nav class="sidebar-menu">
                    <a href="/dashboard" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        Dashboard
                    </a>
                    <a href="/search" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                        Interest Search
                    </a>
                    <a href="/suggestions" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                        </svg>
                        Interest Suggestions
                    </a>
                    <a href="/pool" class="sidebar-item active">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                        </svg>
                        Interest Pool
                    </a>
                </nav>
            </aside>

            <!-- Content -->
            <main class="content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-header-top">
                        <div>
                            <h1 class="page-title">
                                <div class="page-icon">📁</div>
                                Interest Pool
                            </h1>
                            <p class="page-subtitle">Manage your collected interests</p>
                        </div>
                        <div class="page-actions">
                            <button class="btn btn-secondary" onclick="window.history.back()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 18l6-6-6-6"></path>
                                </svg>
                                Back to Tools
                            </button>
                            <button class="btn btn-primary" onclick="window.location.href='/'">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                                    <line x1="4" y1="22" x2="4" y2="15"></line>
                                </svg>
                                Main App
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-value" id="total-interests">2</div>
                        <div class="stat-label">Total Interests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-value" id="total-audience">0</div>
                        <div class="stat-label">Total Audience</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏷️</div>
                        <div class="stat-value" id="interest-types">1</div>
                        <div class="stat-label">Interest Types</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📁</div>
                        <div class="stat-value" id="categories">2</div>
                        <div class="stat-label">Categories</div>
                    </div>
                </div>

                <!-- Collection Info -->
                <div class="collection-info">
                    <div class="info-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                    </div>
                    <div class="info-text">
                        This page shows all interests you've collected from the micro-tools. You can export them to CSV or send them to the main application for use in your Facebook ad campaigns.
                    </div>
                </div>

                <!-- Actions Bar -->
                <div class="actions-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" id="export-csv-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Export to CSV
                        </button>
                        <button class="btn btn-secondary" id="clear-all-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3 6 5 6 21 6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                            Clear All
                        </button>
                        <button class="btn btn-primary" id="send-to-main-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                            </svg>
                            Send to Main App
                        </button>
                    </div>
                    <div class="selection-info">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" class="checkbox" id="select-all-checkbox">
                            <span>Select All</span>
                        </label>
                        <span id="selection-count">0 selected</span>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="filters-header">
                        <h3 class="filters-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                            </svg>
                            Filters
                        </h3>
                        <button class="btn btn-clear-filters" id="clear-filters-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                            Clear Filters
                        </button>
                    </div>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label class="filter-label">Name</label>
                            <input type="text" class="filter-input" id="name-filter" placeholder="Filter by name...">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Audience Size</label>
                            <div class="range-inputs">
                                <input type="text" class="filter-input range-input" id="min-audience" placeholder="Min">
                                <span class="range-separator">to</span>
                                <input type="text" class="filter-input range-input" id="max-audience" placeholder="Max">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Type</label>
                            <select class="filter-input" id="type-filter">
                                <option value="">All Types</option>
                                <option value="interests">Interests</option>
                                <option value="behaviors">Behaviors</option>
                                <option value="demographics">Demographics</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Path</label>
                            <input type="text" class="filter-input" id="path-filter" placeholder="Filter by path...">
                        </div>
                    </div>
                </div>

                <!-- Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">Collected Interests</h3>
                        <span class="stat-label" id="filtered-count">2 items</span>
                    </div>
                    <div class="table-wrapper">
                        <table id="interests-table">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <input type="checkbox" class="checkbox" id="table-select-all">
                                    </th>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Audience Size</th>
                                    <th>Type</th>
                                    <th>Path</th>
                                    <th style="width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="interests-tbody">
                                <!-- Table rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    `;
}

export function initInterestPoolPage() {
    // Sample data for interests
    const interests = [
        {
            id: '6003288647527',
            name: 'Projectors (consumer electronics)',
            audience: 'N/A',
            type: 'interests',
            path: ['Interests', 'Technology', 'Consumer electronics', 'Projectors']
        },
        {
            id: '6002839798079',
            name: 'Slide projector (consumer electronics)',
            audience: 'N/A',
            type: 'interests',
            path: ['Interests', 'Additional Interests', 'Slide projector']
        }
    ];

    let selectedItems = new Set();
    let filteredInterests = [...interests];

    // DOM elements
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const tableSelectAll = document.getElementById('table-select-all');
    const selectionCount = document.getElementById('selection-count');
    const nameFilter = document.getElementById('name-filter');
    const pathFilter = document.getElementById('path-filter');
    const typeFilter = document.getElementById('type-filter');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    const exportCsvBtn = document.getElementById('export-csv-btn');
    const clearAllBtn = document.getElementById('clear-all-btn');
    const sendToMainBtn = document.getElementById('send-to-main-btn');
    const interestsTableBody = document.getElementById('interests-tbody');
    const filteredCount = document.getElementById('filtered-count');

    // Initialize the page
    function init() {
        renderTable();
        setupEventListeners();
        updateStats();
    }

    // Render the interests table
    function renderTable() {
        if (!interestsTableBody) return;

        interestsTableBody.innerHTML = filteredInterests.map(interest => `
            <tr data-id="${interest.id}">
                <td>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" class="checkbox row-checkbox" data-id="${interest.id}">
                    </div>
                </td>
                <td class="interest-id">${interest.id}</td>
                <td class="interest-name">${interest.name}</td>
                <td>
                    <span class="audience-badge">${interest.audience}</span>
                </td>
                <td>
                    <span class="interest-type">${interest.type}</span>
                </td>
                <td>
                    <div class="path-breadcrumb">
                        ${interest.path.map((segment, index) =>
                            `<span>${segment}</span>${index < interest.path.length - 1 ? '<span class="path-separator">›</span>' : ''}`
                        ).join('')}
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn copy-btn" title="Copy ID" data-id="${interest.id}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        </button>
                        <button class="action-btn delete-btn" title="Remove" data-id="${interest.id}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3 6 5 6 21 6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            </svg>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // Update row checkboxes
        updateRowCheckboxes();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Select all functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', handleSelectAll);
        }
        if (tableSelectAll) {
            tableSelectAll.addEventListener('change', handleSelectAll);
        }

        // Row checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('row-checkbox')) {
                handleRowSelect(e.target.dataset.id, e.target.checked);
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.copy-btn')) {
                const id = e.target.closest('.copy-btn').dataset.id;
                handleCopyId(id);
            }
            if (e.target.closest('.delete-btn')) {
                const id = e.target.closest('.delete-btn').dataset.id;
                handleRemoveItem(id);
            }
        });

        // Filters
        if (nameFilter) {
            nameFilter.addEventListener('input', applyFilters);
        }
        if (pathFilter) {
            pathFilter.addEventListener('input', applyFilters);
        }
        if (typeFilter) {
            typeFilter.addEventListener('change', applyFilters);
        }
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', clearFilters);
        }

        // Main action buttons
        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', exportToCSV);
        }
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', clearAllInterests);
        }
        if (sendToMainBtn) {
            sendToMainBtn.addEventListener('click', sendToMainApp);
        }
    }

    // Handle select all
    function handleSelectAll(e) {
        const checked = e.target.checked;
        if (checked) {
            selectedItems = new Set(filteredInterests.map(i => i.id));
        } else {
            selectedItems.clear();
        }
        updateSelectionUI();
    }

    // Handle individual row selection
    function handleRowSelect(id, checked) {
        if (checked) {
            selectedItems.add(id);
        } else {
            selectedItems.delete(id);
        }
        updateSelectionUI();
    }

    // Update selection UI
    function updateSelectionUI() {
        const allSelected = filteredInterests.length > 0 && selectedItems.size === filteredInterests.length;

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allSelected;
        }
        if (tableSelectAll) {
            tableSelectAll.checked = allSelected;
        }
        if (selectionCount) {
            selectionCount.textContent = `${selectedItems.size} selected`;
        }

        updateRowCheckboxes();
    }

    // Update row checkboxes
    function updateRowCheckboxes() {
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.checked = selectedItems.has(checkbox.dataset.id);
        });
    }

    // Apply filters
    function applyFilters() {
        const nameValue = nameFilter?.value.toLowerCase() || '';
        const pathValue = pathFilter?.value.toLowerCase() || '';
        const typeValue = typeFilter?.value || '';

        filteredInterests = interests.filter(interest => {
            const nameMatch = interest.name.toLowerCase().includes(nameValue);
            const pathMatch = interest.path.join(' ').toLowerCase().includes(pathValue);
            const typeMatch = !typeValue || interest.type === typeValue;
            return nameMatch && pathMatch && typeMatch;
        });

        renderTable();
        updateStats();
        updateSelectionUI();
    }

    // Clear filters
    function clearFilters() {
        if (nameFilter) nameFilter.value = '';
        if (pathFilter) pathFilter.value = '';
        if (typeFilter) typeFilter.value = '';
        applyFilters();
    }

    // Update stats
    function updateStats() {
        if (filteredCount) {
            filteredCount.textContent = `${filteredInterests.length} items`;
        }
    }

    // Handle copy ID
    function handleCopyId(id) {
        navigator.clipboard.writeText(id).then(() => {
            window.utils?.showToast('ID copied to clipboard', 'success');
        }).catch(() => {
            window.utils?.showToast('Failed to copy ID', 'error');
        });
    }

    // Handle remove item
    function handleRemoveItem(id) {
        const index = interests.findIndex(i => i.id === id);
        if (index > -1) {
            interests.splice(index, 1);
            selectedItems.delete(id);
            applyFilters();
            window.utils?.showToast('Interest removed', 'success');
        }
    }

    // Export to CSV
    function exportToCSV() {
        const selectedInterests = interests.filter(i => selectedItems.has(i.id));
        const dataToExport = selectedInterests.length > 0 ? selectedInterests : interests;

        const csvContent = [
            ['ID', 'Name', 'Audience Size', 'Type', 'Path'],
            ...dataToExport.map(interest => [
                interest.id,
                interest.name,
                interest.audience,
                interest.type,
                interest.path.join(' > ')
            ])
        ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'interest_pool.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        window.utils?.showToast('CSV exported successfully', 'success');
    }

    // Clear all interests
    function clearAllInterests() {
        if (confirm('Are you sure you want to clear all interests? This action cannot be undone.')) {
            interests.length = 0;
            selectedItems.clear();
            applyFilters();
            window.utils?.showToast('All interests cleared', 'success');
        }
    }

    // Send to main app
    function sendToMainApp() {
        const selectedInterests = interests.filter(i => selectedItems.has(i.id));
        const dataToSend = selectedInterests.length > 0 ? selectedInterests : interests;

        // Store in localStorage for main app to access
        localStorage.setItem('importedInterests', JSON.stringify(dataToSend));

        // Navigate to main app
        window.location.href = '/';

        window.utils?.showToast('Interests sent to main app', 'success');
    }

    // Initialize the page
    init();
}