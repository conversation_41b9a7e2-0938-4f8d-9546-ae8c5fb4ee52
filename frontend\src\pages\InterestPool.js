/**
 * Interest Pool Page - Vanilla JavaScript Implementation
 * Manages collected interests with filtering and export functionality
 */

export function InterestPoolPage() {
    return `
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="/" class="logo">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <circle cx="12" cy="12" r="6"></circle>
                        <circle cx="12" cy="12" r="2"></circle>
                    </svg>
                    TargetWise
                </a>

                <nav class="nav-menu">
                    <a href="/" class="nav-link">Home</a>
                    <a href="/dashboard" class="nav-link">Micro-Tools</a>
                    <a href="/pricing" class="nav-link">Pricing</a>
                    <a href="/docs" class="nav-link">Docs</a>
                </nav>

                <div class="header-actions">
                    <a href="/admin" class="btn btn-outline">Admin</a>
                    <a href="/" class="btn btn-primary">Create Sheet</a>
                    <button class="theme-toggle">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"></circle>
                            <line x1="12" y1="1" x2="12" y2="3"></line>
                            <line x1="12" y1="21" x2="12" y2="23"></line>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                            <line x1="1" y1="12" x2="3" y2="12"></line>
                            <line x1="21" y1="12" x2="23" y2="12"></line>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Container -->
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-title">Micro-Tools</div>
                <nav class="sidebar-menu">
                    <a href="/search" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="M21 21l-4.35-4.35"></path>
                        </svg>
                        Interest Search
                    </a>

                    <a href="/suggestions" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11l3 3L22 4"></path>
                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                        </svg>
                        Interest Suggestions
                    </a>

                    <a href="/taxonomy" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                        </svg>
                        Taxonomy Browser
                    </a>

                    <a href="/pool" class="sidebar-item active">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                        Interest Pool
                    </a>
                </nav>
            </aside>

            <!-- Content -->
            <main class="content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-header-top">
                        <h1 class="page-title">
                            <div class="page-icon">📁</div>
                            Interest Pool
                        </h1>
                        <div class="page-actions">
                            <button class="btn btn-secondary" id="import-csv-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                                Import CSV
                            </button>
                            <button class="btn btn-primary" id="create-pool-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                Create Interest Pool
                            </button>
                        </div>
                    </div>
                    <p class="page-subtitle">Manage and organize your collected interests for Facebook ad targeting campaigns</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-value" id="total-interests">2</div>
                        <div class="stat-label">Total Interests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-value" id="total-audience">0</div>
                        <div class="stat-label">Total Audience</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏷️</div>
                        <div class="stat-value" id="interest-types">1</div>
                        <div class="stat-label">Interest Types</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📁</div>
                        <div class="stat-value" id="categories">2</div>
                        <div class="stat-label">Categories</div>
                    </div>
                </div>

                <!-- Collection Info -->
                <div class="collection-info">
                    <div class="info-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                    </div>
                    <div class="info-text">
                        This page shows all interests you've collected from the micro-tools. You can export them to CSV or send them to the main application for use in your Facebook ad campaigns.
                    </div>
                </div>

                <!-- Actions Bar -->
                <div class="actions-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" id="export-csv-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Export to CSV
                        </button>
                        <button class="btn btn-secondary" id="clear-all-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3 6 5 6 21 6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                            Clear All
                        </button>
                        <button class="btn btn-primary" id="send-to-main-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                            </svg>
                            Send to Main App
                        </button>
                    </div>
                    <div class="selection-info">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" class="checkbox" id="select-all-checkbox">
                            <span>Select All</span>
                        </label>
                        <span id="selection-count">0 selected</span>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="filters-header">
                        <h3 class="filters-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                            </svg>
                            Filters
                        </h3>
                        <button class="btn btn-clear-filters" id="clear-filters-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                            Clear Filters
                        </button>
                    </div>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label class="filter-label">Name</label>
                            <input type="text" class="filter-input" id="name-filter" placeholder="Filter by name...">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Audience Size</label>
                            <div class="range-inputs">
                                <input type="text" class="filter-input range-input" id="min-audience" placeholder="Min">
                                <span class="range-separator">to</span>
                                <input type="text" class="filter-input range-input" id="max-audience" placeholder="Max">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Type</label>
                            <select class="filter-input" id="type-filter">
                                <option value="">All Types</option>
                                <option value="interests">Interests</option>
                                <option value="behaviors">Behaviors</option>
                                <option value="demographics">Demographics</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">Path</label>
                            <input type="text" class="filter-input" id="path-filter" placeholder="Filter by path...">
                        </div>
                    </div>
                </div>

                <!-- Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">Collected Interests</h3>
                        <span class="stat-label" id="filtered-count">2 items</span>
                    </div>
                    <div class="table-wrapper">
                        <table id="interests-table">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <input type="checkbox" class="checkbox" id="table-select-all">
                                    </th>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Audience Size</th>
                                    <th>Type</th>
                                    <th>Path</th>
                                    <th style="width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="interests-tbody">
                                <!-- Table rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    `;
}

export function initInterestPoolPage() {
    // Sample data for interests
    const interests = [
        {
            id: '6003288647527',
            name: 'Projectors (consumer electronics)',
            audience: 'N/A',
            type: 'interests',
            path: ['Interests', 'Technology', 'Consumer electronics', 'Projectors']
        },
        {
            id: '6002839798079',
            name: 'Slide projector (consumer electronics)',
            audience: 'N/A',
            type: 'interests',
            path: ['Interests', 'Additional Interests', 'Slide projector']
        }
    ];

    let selectedItems = new Set();
    let filteredInterests = [...interests];

    // DOM elements
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const tableSelectAll = document.getElementById('table-select-all');
    const selectionCount = document.getElementById('selection-count');
    const nameFilter = document.getElementById('name-filter');
    const pathFilter = document.getElementById('path-filter');
    const typeFilter = document.getElementById('type-filter');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    const exportCsvBtn = document.getElementById('export-csv-btn');
    const clearAllBtn = document.getElementById('clear-all-btn');
    const sendToMainBtn = document.getElementById('send-to-main-btn');
    const interestsTableBody = document.getElementById('interests-tbody');
    const filteredCount = document.getElementById('filtered-count');

    // Initialize the page
    function init() {
        renderTable();
        setupEventListeners();
        updateStats();
    }

    // Render the interests table
    function renderTable() {
        if (!interestsTableBody) return;

        interestsTableBody.innerHTML = filteredInterests.map(interest => `
            <tr data-id="${interest.id}">
                <td>
                    <div class="checkbox-wrapper">
                        <input type="checkbox" class="checkbox row-checkbox" data-id="${interest.id}">
                    </div>
                </td>
                <td class="interest-id">${interest.id}</td>
                <td class="interest-name">${interest.name}</td>
                <td>
                    <span class="audience-badge">${interest.audience}</span>
                </td>
                <td>
                    <span class="interest-type">${interest.type}</span>
                </td>
                <td>
                    <div class="path-breadcrumb">
                        ${interest.path.map((segment, index) =>
                            `<span>${segment}</span>${index < interest.path.length - 1 ? '<span class="path-separator">›</span>' : ''}`
                        ).join('')}
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn copy-btn" title="Copy ID" data-id="${interest.id}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        </button>
                        <button class="action-btn delete-btn" title="Remove" data-id="${interest.id}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3 6 5 6 21 6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            </svg>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // Update row checkboxes
        updateRowCheckboxes();
    }

    // Setup event listeners
    function setupEventListeners() {
        // Select all functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', handleSelectAll);
        }
        if (tableSelectAll) {
            tableSelectAll.addEventListener('change', handleSelectAll);
        }

        // Row checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('row-checkbox')) {
                handleRowSelect(e.target.dataset.id, e.target.checked);
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.copy-btn')) {
                const id = e.target.closest('.copy-btn').dataset.id;
                handleCopyId(id);
            }
            if (e.target.closest('.delete-btn')) {
                const id = e.target.closest('.delete-btn').dataset.id;
                handleRemoveItem(id);
            }
        });

        // Filters
        if (nameFilter) {
            nameFilter.addEventListener('input', applyFilters);
        }
        if (pathFilter) {
            pathFilter.addEventListener('input', applyFilters);
        }
        if (typeFilter) {
            typeFilter.addEventListener('change', applyFilters);
        }
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', clearFilters);
        }

        // Main action buttons
        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', exportToCSV);
        }
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', clearAllInterests);
        }
        if (sendToMainBtn) {
            sendToMainBtn.addEventListener('click', sendToMainApp);
        }
    }

    // Handle select all
    function handleSelectAll(e) {
        const checked = e.target.checked;
        if (checked) {
            selectedItems = new Set(filteredInterests.map(i => i.id));
        } else {
            selectedItems.clear();
        }
        updateSelectionUI();
    }

    // Handle individual row selection
    function handleRowSelect(id, checked) {
        if (checked) {
            selectedItems.add(id);
        } else {
            selectedItems.delete(id);
        }
        updateSelectionUI();
    }

    // Update selection UI
    function updateSelectionUI() {
        const allSelected = filteredInterests.length > 0 && selectedItems.size === filteredInterests.length;

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allSelected;
        }
        if (tableSelectAll) {
            tableSelectAll.checked = allSelected;
        }
        if (selectionCount) {
            selectionCount.textContent = `${selectedItems.size} selected`;
        }

        updateRowCheckboxes();
    }

    // Update row checkboxes
    function updateRowCheckboxes() {
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.checked = selectedItems.has(checkbox.dataset.id);
        });
    }

    // Apply filters
    function applyFilters() {
        const nameValue = nameFilter?.value.toLowerCase() || '';
        const pathValue = pathFilter?.value.toLowerCase() || '';
        const typeValue = typeFilter?.value || '';

        filteredInterests = interests.filter(interest => {
            const nameMatch = interest.name.toLowerCase().includes(nameValue);
            const pathMatch = interest.path.join(' ').toLowerCase().includes(pathValue);
            const typeMatch = !typeValue || interest.type === typeValue;
            return nameMatch && pathMatch && typeMatch;
        });

        renderTable();
        updateStats();
        updateSelectionUI();
    }

    // Clear filters
    function clearFilters() {
        if (nameFilter) nameFilter.value = '';
        if (pathFilter) pathFilter.value = '';
        if (typeFilter) typeFilter.value = '';
        applyFilters();
    }

    // Update stats
    function updateStats() {
        if (filteredCount) {
            filteredCount.textContent = `${filteredInterests.length} items`;
        }
    }

    // Handle copy ID
    function handleCopyId(id) {
        navigator.clipboard.writeText(id).then(() => {
            window.utils?.showToast('ID copied to clipboard', 'success');
        }).catch(() => {
            window.utils?.showToast('Failed to copy ID', 'error');
        });
    }

    // Handle remove item
    function handleRemoveItem(id) {
        const index = interests.findIndex(i => i.id === id);
        if (index > -1) {
            interests.splice(index, 1);
            selectedItems.delete(id);
            applyFilters();
            window.utils?.showToast('Interest removed', 'success');
        }
    }

    // Export to CSV
    function exportToCSV() {
        const selectedInterests = interests.filter(i => selectedItems.has(i.id));
        const dataToExport = selectedInterests.length > 0 ? selectedInterests : interests;

        const csvContent = [
            ['ID', 'Name', 'Audience Size', 'Type', 'Path'],
            ...dataToExport.map(interest => [
                interest.id,
                interest.name,
                interest.audience,
                interest.type,
                interest.path.join(' > ')
            ])
        ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'interest_pool.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        window.utils?.showToast('CSV exported successfully', 'success');
    }

    // Clear all interests
    function clearAllInterests() {
        if (confirm('Are you sure you want to clear all interests? This action cannot be undone.')) {
            interests.length = 0;
            selectedItems.clear();
            applyFilters();
            window.utils?.showToast('All interests cleared', 'success');
        }
    }

    // Send to main app
    function sendToMainApp() {
        const selectedInterests = interests.filter(i => selectedItems.has(i.id));
        const dataToSend = selectedInterests.length > 0 ? selectedInterests : interests;

        // Store in localStorage for main app to access
        localStorage.setItem('importedInterests', JSON.stringify(dataToSend));

        // Navigate to main app
        window.location.href = '/';

        window.utils?.showToast('Interests sent to main app', 'success');
    }

    // Initialize the page
    init();
}