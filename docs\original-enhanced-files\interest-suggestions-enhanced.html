<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interest Suggestions - TargetWise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f0f2f5;
            color: #1a1a2e;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .logo:hover {
            transform: translateY(-1px);
        }

        .logo svg {
            width: 32px;
            height: 32px;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .nav-link {
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .nav-link:hover {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.08);
        }

        .nav-link.active {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .theme-toggle {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: none;
            background: #f1f5f9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: #e2e8f0;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline {
            background: transparent;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        .btn-outline:hover {
            border-color: #cbd5e1;
            background: #f8fafc;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
            box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
        }

        /* Main Layout */
        .main-container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            gap: 24px;
            padding: 24px;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 10px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: all 0.2s;
        }

        .sidebar-item:hover {
            background: #f1f5f9;
            color: #1e293b;
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            color: #2563eb;
        }

        .sidebar-icon {
            width: 20px;
            height: 20px;
            opacity: 0.7;
        }

        /* Content Area */
        .content {
            flex: 1;
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .page-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-left: 64px;
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .staging-cart {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            color: white;
            position: relative;
        }

        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc2626;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 700;
        }

        /* Main Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        /* Seed Library Panel */
        .seed-library {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .panel-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }

        .panel-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .search-box {
            margin-bottom: 24px;
        }

        .search-input-wrapper {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 14px 20px 14px 48px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
        }

        .seed-list {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .seed-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-radius: 10px;
            background: #f8fafc;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .seed-item:hover {
            background: #f1f5f9;
            transform: translateX(4px);
        }

        .seed-item.selected {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            border: 2px solid #2563eb;
        }

        .seed-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .seed-icon {
            width: 36px;
            height: 36px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .seed-name {
            font-weight: 500;
            color: #1e293b;
        }

        .seed-category {
            font-size: 13px;
            color: #64748b;
        }

        .seed-count {
            background: #2563eb;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        .no-seeds {
            text-align: center;
            padding: 40px;
            color: #94a3b8;
        }

        /* Suggestions Panel */
        .suggestions-panel {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .tabs {
            display: flex;
            gap: 8px;
            background: #f1f5f9;
            padding: 4px;
            border-radius: 10px;
            margin-bottom: 24px;
        }

        .tab {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            background: transparent;
            color: #64748b;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            flex: 1;
        }

        .tab.active {
            background: white;
            color: #2563eb;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
            margin-bottom: 8px;
            display: block;
        }

        .select-wrapper {
            position: relative;
        }

        .form-select {
            width: 100%;
            padding: 14px 20px;
            padding-right: 48px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            appearance: none;
            transition: all 0.2s;
        }

        .form-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .select-arrow {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            color: #94a3b8;
        }

        .btn-get-suggestions {
            width: 100%;
            padding: 16px;
            font-size: 16px;
            margin-bottom: 24px;
        }

        /* Suggestions Results */
        .suggestions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .suggestions-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .suggestions-count {
            background: #f1f5f9;
            color: #475569;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        .suggestion-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-height: 400px;
            overflow-y: auto;
        }

        .suggestion-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.2s;
        }

        .suggestion-item:hover {
            border-color: #2563eb;
            background: rgba(37, 99, 235, 0.02);
        }

        .suggestion-item.selected {
            border-color: #2563eb;
            background: rgba(37, 99, 235, 0.05);
        }

        .suggestion-content {
            flex: 1;
        }

        .suggestion-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .suggestion-meta {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .suggestion-stat {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;
            color: #64748b;
        }

        .suggestion-stat-icon {
            width: 16px;
            height: 16px;
            opacity: 0.6;
        }

        .suggestion-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #2563eb;
            color: #2563eb;
            background: rgba(37, 99, 235, 0.05);
        }

        .action-btn.add-btn {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .action-btn.add-btn:hover {
            background: #1d4ed8;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            background: #f1f5f9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 36px;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .empty-text {
            color: #64748b;
        }

        /* Loading State */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f4f6;
            border-top-color: #2563eb;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Bulk Actions Bar */
        .bulk-actions {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #3b82f6;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .bulk-info {
            font-weight: 500;
            color: #1e40af;
        }

        .bulk-buttons {
            display: flex;
            gap: 8px;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
        }

        @media (max-width: 640px) {
            .header-content {
                padding: 12px 16px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .page-header {
                padding: 24px 16px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .seed-library,
            .suggestions-panel {
                padding: 24px 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <circle cx="12" cy="12" r="6"></circle>
                    <circle cx="12" cy="12" r="2"></circle>
                </svg>
                TargetWise
            </a>
            
            <nav class="nav-menu">
                <a href="#" class="nav-link">Home</a>
                <a href="#" class="nav-link active">Micro-Tools</a>
                <a href="#" class="nav-link">Documentation</a>
                <a href="#" class="nav-link">Pricing</a>
                <a href="#" class="nav-link">Admin</a>
            </nav>
            
            <div class="header-actions">
                <button class="theme-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
                <a href="#" class="btn btn-outline">Log In</a>
                <a href="#" class="btn btn-primary">Sign Up Free</a>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3 class="sidebar-title">Micro-Tools</h3>
            <nav class="sidebar-menu">
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    Dashboard
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    Interest Search
                </a>
                <a href="#" class="sidebar-item active">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                    </svg>
                    Interest Suggestions
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 3v18h18"></path>
                        <path d="m19 9-5 5-4-4-3 3"></path>
                    </svg>
                    Taxonomy Browser
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                    </svg>
                    Interest Pool
                </a>
            </nav>
        </aside>

        <!-- Content -->
        <main class="content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-header-top">
                    <div>
                        <h1 class="page-title">
                            <div class="page-icon">💡</div>
                            Interest Suggestions
                        </h1>
                        <p class="page-subtitle">Discover related interests based on seed interests</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 18l6-6-6-6"></path>
                            </svg>
                            Back to Tools
                        </button>
                        <button class="btn staging-cart">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="9" cy="21" r="1"></circle>
                                <circle cx="20" cy="21" r="1"></circle>
                                <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                            </svg>
                            Staging Cart
                            <span class="cart-badge">0</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Seed Library -->
                <div class="seed-library">
                    <div class="panel-header">
                        <div class="panel-icon">🌱</div>
                        <h2 class="panel-title">Seed Library</h2>
                    </div>

                    <div class="search-box">
                        <div class="search-input-wrapper">
                            <svg class="search-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            <input type="text" class="search-input" placeholder="Search for a seed interest">
                        </div>
                    </div>

                    <div class="seed-list">
                        <div class="seed-item">
                            <div class="seed-info">
                                <div class="seed-icon">🎬</div>
                                <div>
                                    <div class="seed-name">Movies</div>
                                    <div class="seed-category">Entertainment</div>
                                </div>
                            </div>
                            <span class="seed-count">850M</span>
                        </div>
                        <div class="seed-item">
                            <div class="seed-info">
                                <div class="seed-icon">📱</div>
                                <div>
                                    <div class="seed-name">Technology</div>
                                    <div class="seed-category">Interests</div>
                                </div>
                            </div>
                            <span class="seed-count">720M</span>
                        </div>
                        <div class="seed-item">
                            <div class="seed-info">
                                <div class="seed-icon">🏃</div>
                                <div>
                                    <div class="seed-name">Fitness</div>
                                    <div class="seed-category">Health & Wellness</div>
                                </div>
                            </div>
                            <span class="seed-count">540M</span>
                        </div>
                        <div class="seed-item">
                            <div class="seed-info">
                                <div class="seed-icon">🍳</div>
                                <div>
                                    <div class="seed-name">Cooking</div>
                                    <div class="seed-category">Food & Drink</div>
                                </div>
                            </div>
                            <span class="seed-count">430M</span>
                        </div>
                        <div class="seed-item">
                            <div class="seed-info">
                                <div class="seed-icon">✈️</div>
                                <div>
                                    <div class="seed-name">Travel</div>
                                    <div class="seed-category">Lifestyle</div>
                                </div>
                            </div>
                            <span class="seed-count">380M</span>
                        </div>
                    </div>
                </div>

                <!-- Suggestions Panel -->
                <div class="suggestions-panel">
                    <div class="panel-header">
                        <div class="panel-icon">🎯</div>
                        <h2 class="panel-title">Get Suggestions</h2>
                    </div>

                    <div class="tabs">
                        <button class="tab active" id="singleTab">Single Suggestions</button>
                        <button class="tab" id="bulkTab">Bulk Suggestions</button>
                    </div>

                    <form class="suggestions-form">
                        <div class="form-group">
                            <label class="form-label">Select a seed interest</label>
                            <div class="select-wrapper">
                                <select class="form-select" id="seedSelect">
                                    <option value="">-- Select a seed interest --</option>
                                    <option value="movies">Movies (Entertainment)</option>
                                    <option value="technology">Technology (Interests)</option>
                                    <option value="fitness">Fitness (Health & Wellness)</option>
                                    <option value="cooking">Cooking (Food & Drink)</option>
                                    <option value="travel">Travel (Lifestyle)</option>
                                </select>
                                <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-get-suggestions">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                            </svg>
                            Get Suggestions
                        </button>
                    </form>

                    <div class="suggestions-section">
                        <div class="suggestions-header">
                            <h3 class="suggestions-title">Suggestions</h3>
                            <span class="suggestions-count">0 results</span>
                        </div>

                        <!-- Empty State -->
                        <div class="empty-state">
                            <div class="empty-icon">💡</div>
                            <h4 class="empty-title">No suggestions yet</h4>
                            <p class="empty-text">Select a seed interest and click "Get Suggestions" to see related interests</p>
                        </div>

                        <!-- Results (hidden by default) -->
                        <div class="suggestion-list" style="display: none;">
                            <!-- Suggestions will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Tab switching
        const singleTab = document.getElementById('singleTab');
        const bulkTab = document.getElementById('bulkTab');

        singleTab.addEventListener('click', () => {
            singleTab.classList.add('active');
            bulkTab.classList.remove('active');
        });

        bulkTab.addEventListener('click', () => {
            bulkTab.classList.add('active');
            singleTab.classList.remove('active');
        });

        // Seed item selection
        document.querySelectorAll('.seed-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.seed-item').forEach(i => i.classList.remove('selected'));
                
                // Add selection to clicked item
                this.classList.add('selected');
                
                // Update dropdown
                const seedName = this.querySelector('.seed-name').textContent.toLowerCase();
                document.getElementById('seedSelect').value = seedName;
            });
        });

        // Form submission
        document.querySelector('.suggestions-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const seedSelect = document.getElementById('seedSelect');
            if (!seedSelect.value) {
                seedSelect.focus();
                return;
            }
            
            // Show loading state
            const suggestionsSection = document.querySelector('.suggestions-section');
            const emptyState = suggestionsSection.querySelector('.empty-state');
            const suggestionList = suggestionsSection.querySelector('.suggestion-list');
            
            emptyState.style.display = 'none';
            suggestionList.style.display = 'none';
            
            // Add loading spinner
            suggestionsSection.insertAdjacentHTML('beforeend', '<div class="loading"><div class="spinner"></div></div>');
            
            // Simulate API call
            setTimeout(() => {
                const loading = suggestionsSection.querySelector('.loading');
                if (loading) loading.remove();
                
                // Show results
                suggestionList.style.display = 'flex';
                document.querySelector('.suggestions-count').textContent = '8 results';
                
                // Populate sample suggestions
                const suggestions = [
                    { name: 'Netflix', audience: '420M', category: 'Streaming' },
                    { name: 'HBO', audience: '180M', category: 'Entertainment' },
                    { name: 'Film Festival', audience: '125M', category: 'Events' },
                    { name: 'IMAX', audience: '95M', category: 'Cinema' },
                    { name: 'Popcorn', audience: '340M', category: 'Food' },
                    { name: 'Red Carpet', audience: '78M', category: 'Entertainment' },
                    { name: 'Action Movies', audience: '560M', category: 'Genre' },
                    { name: 'Documentary', audience: '290M', category: 'Genre' }
                ];
                
                suggestionList.innerHTML = suggestions.map((sug, index) => `
                    <div class="suggestion-item">
                        <div class="suggestion-content">
                            <div class="suggestion-name">${sug.name}</div>
                            <div class="suggestion-meta">
                                <span class="suggestion-stat">
                                    <svg class="suggestion-stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="9" cy="7" r="4"></circle>
                                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                    </svg>
                                    ${sug.audience}
                                </span>
                                <span class="suggestion-stat">
                                    <svg class="suggestion-stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M3 3h18v18H3zM12 8v8m-4-4h8"></path>
                                    </svg>
                                    ${sug.category}
                                </span>
                            </div>
                        </div>
                        <div class="suggestion-actions">
                            <button class="action-btn" title="Add to favorites">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                </svg>
                            </button>
                            <button class="action-btn add-btn" title="Add to cart">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
                
                // Add click handlers for new items
                addSuggestionHandlers();
            }, 1500);
        });

        // Add handlers for suggestion items
        function addSuggestionHandlers() {
            // Selection
            document.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    if (!e.target.closest('.action-btn')) {
                        this.classList.toggle('selected');
                        updateBulkActions();
                    }
                });
            });
            
            // Add to cart
            document.querySelectorAll('.add-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // Update cart count
                    const cartBadge = document.querySelector('.cart-badge');
                    const currentCount = parseInt(cartBadge.textContent);
                    cartBadge.textContent = currentCount + 1;
                    
                    // Animate button
                    this.style.transform = 'scale(0.8)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });
        }

        // Bulk actions
        function updateBulkActions() {
            const selectedCount = document.querySelectorAll('.suggestion-item.selected').length;
            const suggestionsSection = document.querySelector('.suggestions-section');
            let bulkActions = suggestionsSection.querySelector('.bulk-actions');
            
            if (selectedCount > 0) {
                if (!bulkActions) {
                    const bulkHTML = `
                        <div class="bulk-actions">
                            <span class="bulk-info">${selectedCount} items selected</span>
                            <div class="bulk-buttons">
                                <button class="btn btn-secondary" onclick="deselectAll()">Deselect All</button>
                                <button class="btn btn-primary" onclick="addSelectedToCart()">Add to Cart</button>
                            </div>
                        </div>
                    `;
                    suggestionsSection.insertAdjacentHTML('afterbegin', bulkHTML);
                } else {
                    bulkActions.querySelector('.bulk-info').textContent = `${selectedCount} items selected`;
                }
            } else {
                if (bulkActions) {
                    bulkActions.remove();
                }
            }
        }

        function deselectAll() {
            document.querySelectorAll('.suggestion-item.selected').forEach(item => {
                item.classList.remove('selected');
            });
            updateBulkActions();
        }

        function addSelectedToCart() {
            const selectedCount = document.querySelectorAll('.suggestion-item.selected').length;
            const cartBadge = document.querySelector('.cart-badge');
            const currentCount = parseInt(cartBadge.textContent);
            cartBadge.textContent = currentCount + selectedCount;
            
            deselectAll();
        }

        // Search functionality
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            
            document.querySelectorAll('.seed-item').forEach(item => {
                const name = item.querySelector('.seed-name').textContent.toLowerCase();
                const category = item.querySelector('.seed-category').textContent.toLowerCase();
                
                if (name.includes(searchTerm) || category.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>