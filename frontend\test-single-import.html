<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Single Import Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .loading { color: blue; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-card">
        <h1>Single Import Test</h1>
        <div id="status" class="loading">Testing single page import...</div>
        <div id="details"></div>
    </div>
    
    <script type="module">
        console.log('Testing single page import...');
        
        try {
            // Test importing just the TargetWiseMain page
            document.getElementById('status').innerHTML = '<span class="loading">Importing TargetWiseMain.js...</span>';
            
            const { TargetWiseMainPage, initTargetWiseMainPage } = await import('./src/pages/TargetWiseMain.js');
            console.log('TargetWiseMain imported successfully');
            
            document.getElementById('status').innerHTML = '<span class="success">✅ TargetWiseMain imported successfully!</span>';
            document.getElementById('details').innerHTML = '<p>Functions available: TargetWiseMainPage, initTargetWiseMainPage</p>';
            
        } catch (error) {
            console.error('Import failed:', error);
            document.getElementById('status').innerHTML = '<span class="error">❌ Import failed</span>';
            document.getElementById('details').innerHTML = '<p>Error: ' + error.message + '</p>';
        }
    </script>
</body>
</html>
