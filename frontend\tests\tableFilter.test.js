// DOC: docs/micro-tools/search-bulk-feature.md
/**
 * @jest-environment jsdom
 */
require('../../static/js/modules/micro-tools/components/TableFilter.js');
const TableFilter = window.TableFilter;

describe('TableFilter', () => {
  let onFilterMock;
  let data;

  beforeEach(() => {
    document.body.innerHTML = '<div id="filterContainer"></div>';
    onFilterMock = jest.fn();
    data = [
      { name: 'Alpha', audience_size_lower_bound: 100, audience_size_upper_bound: 200, type: 'A', path: ['X'] },
      { name: 'Beta', audience_size_lower_bound: 300, audience_size_upper_bound: 400, type: 'B', path: ['Y'] },
      { name: 'Gamma', audience_size_lower_bound: 500, audience_size_upper_bound: 600, type: 'A', path: ['X', 'Z'] },
    ];
  });

  test('getUniqueTypes returns correct unique types', () => {
    const tf = new TableFilter({
      containerId: 'filterContainer',
      data,
      onFilter: onFilterMock,
      filterOptions: {},
    });
    const types = tf.getUniqueTypes();
    expect(types.sort()).toEqual(['a', 'b']);
  });

  test('applyFilters filters by name, audience size, type, and path', () => {
    const tf = new TableFilter({
      containerId: 'filterContainer',
      data,
      onFilter: onFilterMock,
      filterOptions: { name: true, audienceSize: true, type: true, path: true },
    });
    tf.filters = {
      name: 'a',
      audienceSizeMin: 200,
      audienceSizeMax: 600,
      type: 'a',
      path: 'x',
    };
    tf.applyFilters();
    expect(onFilterMock).toHaveBeenCalledWith([data[2]]);
  });

  test('clearFilters resets filters and calls onFilter with full data', () => {
    const tf = new TableFilter({
      containerId: 'filterContainer',
      data,
      onFilter: onFilterMock,
      filterOptions: { name: true, audienceSize: true, type: true, path: true },
    });
    tf.filters.name = 'x';
    tf.clearFilters();
    expect(tf.filters).toEqual({
      name: '',
      audienceSizeMin: '',
      audienceSizeMax: '',
      type: '',
      path: '',
    });
    expect(onFilterMock).toHaveBeenCalledWith(data);
  });

  test('updateData replaces data and applies filters', () => {
    const tf = new TableFilter({
      containerId: 'filterContainer',
      data: [],
      onFilter: onFilterMock,
      filterOptions: { type: true },
    });
    tf.updateData(data);
    expect(onFilterMock).toHaveBeenCalled();
    const typeSelect = document.getElementById('filterContainer-type-filter');
    expect(typeSelect.children.length).toBeGreaterThan(1);
  });
});