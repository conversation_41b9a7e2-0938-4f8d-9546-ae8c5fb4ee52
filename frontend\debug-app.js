/**
 * Debug App - Import pages one by one to find the issue
 */

console.log('Debug app starting...');

// Test imports one by one
async function testImports() {
  const results = [];
  
  try {
    console.log('Testing TargetWiseMain import...');
    const main = await import('./src/pages/TargetWiseMain.js');
    results.push('✅ TargetWiseMain.js - OK');
    console.log('TargetWiseMain imported successfully');
  } catch (error) {
    results.push('❌ TargetWiseMain.js - ' + error.message);
    console.error('TargetWiseMain import failed:', error);
  }
  
  try {
    console.log('Testing Dashboard import...');
    const dashboard = await import('./src/pages/Dashboard.js');
    results.push('✅ Dashboard.js - OK');
    console.log('Dashboard imported successfully');
  } catch (error) {
    results.push('❌ Dashboard.js - ' + error.message);
    console.error('Dashboard import failed:', error);
  }
  
  try {
    console.log('Testing InterestPool import...');
    const pool = await import('./src/pages/InterestPool.js');
    results.push('✅ InterestPool.js - OK');
    console.log('InterestPool imported successfully');
  } catch (error) {
    results.push('❌ InterestPool.js - ' + error.message);
    console.error('InterestPool import failed:', error);
  }
  
  try {
    console.log('Testing InterestSuggestions import...');
    const suggestions = await import('./src/pages/InterestSuggestions.js');
    results.push('✅ InterestSuggestions.js - OK');
    console.log('InterestSuggestions imported successfully');
  } catch (error) {
    results.push('❌ InterestSuggestions.js - ' + error.message);
    console.error('InterestSuggestions import failed:', error);
  }
  
  try {
    console.log('Testing InterestSearchEnhanced import...');
    const search = await import('./src/pages/InterestSearchEnhanced.js');
    results.push('✅ InterestSearchEnhanced.js - OK');
    console.log('InterestSearchEnhanced imported successfully');
  } catch (error) {
    results.push('❌ InterestSearchEnhanced.js - ' + error.message);
    console.error('InterestSearchEnhanced import failed:', error);
  }
  
  return results;
}

// Initialize debug app
async function initDebugApp() {
  console.log('Initializing debug app...');
  
  const appElement = document.getElementById('app');
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1 style="color: #2563eb;">🔍 Debug App - Import Testing</h1>
        <p>Testing each page import individually...</p>
        <div id="results" style="margin-top: 20px;">
          <p>Running tests...</p>
        </div>
      </div>
    `;
    
    // Test imports
    const results = await testImports();
    
    // Display results
    const resultsDiv = document.getElementById('results');
    if (resultsDiv) {
      resultsDiv.innerHTML = `
        <h3>Import Test Results:</h3>
        <ul style="list-style: none; padding: 0;">
          ${results.map(result => `<li style="margin: 5px 0; padding: 5px; background: ${result.includes('✅') ? '#d4edda' : '#f8d7da'}; border-radius: 4px;">${result}</li>`).join('')}
        </ul>
        <p style="margin-top: 20px;"><strong>Total: ${results.filter(r => r.includes('✅')).length}/${results.length} imports successful</strong></p>
      `;
    }
    
    console.log('Debug app initialized successfully');
  } else {
    console.error('App element not found');
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initDebugApp);
} else {
  initDebugApp();
}

export { initDebugApp };
