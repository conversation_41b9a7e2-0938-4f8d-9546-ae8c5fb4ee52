// tests/playwright/micro-tools.spec.ts
import { test, expect } from '@playwright/test';

const microToolsPages = [
  '/micro-tools',
  '/micro-tools/search',
  '/micro-tools/suggestions',
  '/micro-tools/browse',
  '/micro-tools/interest-pool',
  '/design-system',
];

test.describe('TargetWise Micro-Tools & Design System', () => {
  for (const url of microToolsPages) {
    test(`Page ${url} loads and main UI is visible`, async ({ page }) => {
      await page.goto(url);
      // Check for a main heading or unique element on each page
      if (url === '/micro-tools') {
        await expect(page.getByRole('heading', { name: /Micro-Tools/i })).toBeVisible();
      } else if (url === '/micro-tools/search') {
        await expect(page.getByRole('heading', { name: /Interest Search/i })).toBeVisible();
      } else if (url === '/micro-tools/suggestions') {
        await expect(page.getByRole('heading', { name: /Interest Suggestions/i })).toBeVisible();
      } else if (url === '/micro-tools/browse') {
        await expect(page.getByRole('heading', { name: /Taxonomy Browser/i })).toBeVisible();
      } else if (url === '/micro-tools/interest-pool') {
        await expect(page.getByRole('heading', { name: /Staging Cart|Interest Pool/i })).toBeVisible();
      } else if (url === '/design-system') {
        await expect(page.getByRole('heading', { name: /Design System/i })).toBeVisible();
      }
    });
  }
});
