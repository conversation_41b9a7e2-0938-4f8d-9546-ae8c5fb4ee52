#!/usr/bin/env python3
"""Mock API server to handle frontend requests during development"""

import http.server
import socketserver
import json
import urllib.parse
from datetime import datetime

PORT = 8000

class MockAPIHandler(http.server.BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_GET(self):
        # Add CORS headers
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        # Mock responses for different endpoints
        path = self.path.split('?')[0]
        
        if path == '/api/v1/health':
            response = {"status": "healthy", "timestamp": datetime.now().isoformat()}
        elif path == '/api/v1/search/interests':
            response = {
                "interests": [
                    {"id": "6003139266461", "name": "Movies", "audience_size": **********},
                    {"id": "6003107902433", "name": "Music", "audience_size": **********},
                    {"id": "6003029058045", "name": "Sports", "audience_size": **********}
                ],
                "total": 3
            }
        elif path == '/api/v1/suggestions':
            response = {
                "suggestions": [
                    {"name": "Technology", "category": "Interests", "relevance": 0.9},
                    {"name": "Gaming", "category": "Interests", "relevance": 0.85},
                    {"name": "Travel", "category": "Interests", "relevance": 0.8}
                ]
            }
        elif path == '/api/v1/pool':
            response = {
                "items": [],
                "total": 0
            }
        else:
            response = {"message": "Mock API endpoint", "path": path}
        
        self.wfile.write(json.dumps(response).encode())

    def do_POST(self):
        # Add CORS headers
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        # Return success for all POST requests
        response = {"success": True, "message": "Mock API received POST request"}
        self.wfile.write(json.dumps(response).encode())

    def log_message(self, format, *args):
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

print(f"Mock API server running at http://localhost:{PORT}/")
print("This is a mock server for development. Start the real API server for full functionality.")

with socketserver.TCPServer(("", PORT), MockAPIHandler) as httpd:
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nMock API server stopped.")