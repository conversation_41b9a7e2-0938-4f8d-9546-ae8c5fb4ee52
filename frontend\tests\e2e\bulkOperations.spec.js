// DOC: docs/micro-tools/search-bulk-feature.md
const { test, expect } = require('@playwright/test');

test.describe('Bulk Operations Workflow', () => {
  test('select multiple → apply actions → verify results', async ({ page }) => {
    await page.goto('/micro-tools/search');
    await page.fill('#searchInput', 'Cars');
    await page.click('#searchButton');
    await page.waitForSelector('.row-select');
    const rows = await page.$$('.row-select');
    expect(rows.length).toBeGreaterThan(1);
    await rows[0].check();
    await rows[1].check();
    await page.click('#addSelectedBtn');
    await expect(page.locator('#addSelectedBtn')).toContainText('Add Selected (2)');
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.click('#exportSelectedBtn'),
    ]);
    expect(download.suggestedFilename()).toBe('selected_interests.csv');
  });
});