[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "targetwise"
version = "1.0.0"
description = "Facebook Algorithmic Targeting 2.0 Platform"
readme = "README.md"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "fastapi>=0.110.0",
    "uvicorn>=0.27.1",
    "python-multipart>=0.0.9",
    "requests>=2.31.0",
    "pandas>=2.2.1",
    "openpyxl>=3.1.2",
    "python-dotenv>=1.0.1",
    "pydantic>=2.6.3",
    "pydantic-settings>=2.2.1",
    "sentence-transformers>=2.5.1",
    "numpy>=1.26.4",
    "scikit-learn>=1.4.1",
    "fpdf2>=2.7.8",
    "httpx>=0.27.0",
    "redis>=5.0.4",
    "jinja2>=3.1.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.1",
    "pytest-asyncio>=0.23.5",
    "black>=24.1.1",
    "isort>=5.13.2",
    "flake8>=7.0.0",
    "pre-commit>=3.6.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "ipython>=8.18.1",
    "jupyter>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/targetwise"
Bug Tracker = "https://github.com/yourusername/targetwise/issues"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.git
  | \.mypy_cache
  | \.pytest_cache
  | \.venv
  | build
  | dist
  | venv
  | node_modules
  | \.tox
)/
'''

[tool.isort]
profile = "black"
line_length = 88
skip = [
    ".git",
    ".mypy_cache",
    ".pytest_cache",
    ".venv",
    "build",
    "dist",
    "venv",
    "node_modules",
    ".tox",
]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[[tool.mypy.overrides]]
module = [
    "jupyter*",
    "IPython*",
    "pandas*",
    "numpy*",
]
ignore_missing_imports = true

test = [
    "test_*.py",
    "tests/*.py",
    "tests/**/*.py",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_functions = ["test_*"]
python_classes = ["Test*"]
addopts = "-v -s --cov=app --cov-report=term-missing"

[tool.coverage.run]
source = ["app"]
omit = [
    "app/tests/*",
    "app/__main__.py",
    "**/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
    "if TYPE_CHECKING:",
    "@abstractmethod",
]

[flake8]
max-line-length = 88
extend-ignore = "E203, W503"
exclude = [
    ".git",
    "__pycache__",
    ".pytest_cache",
    ".mypy_cache",
    "build",
    "dist",
    ".venv",
    "venv",
    "node_modules",
    "migrations",
]
per-file-ignores = [
    "__init__.py: F401",
]
