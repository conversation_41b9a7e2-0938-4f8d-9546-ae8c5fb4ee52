// DOC: docs/development/terminal-notifications.md
/**
 * Simple terminal notification utility using Chalk for colored output.
 */

const chalk = require('chalk');

const notify = {
  process: {
    serverStarted: port => console.log(chalk.cyan(`🚀 Server started on port ${port}`)),
    dbConnected: () => console.log(chalk.yellow(`⚡ Connected to database`)),
    webpackDone: () => console.log(chalk.green(`✅ Webpack compilation complete`))
  },
  build: {
    start: () => console.log(chalk.blue(`📦 Building application...`)),
    complete: time => console.log(chalk.green(`✅ Build complete in ${time}`))
  },
  audit: {
    start: () => console.log(chalk.magenta(`🔍 Running npm audit...`)),
    critical: count => console.log(chalk.red(`⚠️ Critical vulnerabilities found: ${count}`)),
    moderate: count => console.log(chalk.yellow(`⚠️ Moderate vulnerabilities found: ${count}`)),
    bundleWarn: size => console.log(chalk.yellow(`📏 Bundle size exceeds threshold: ${size}`)),
    perf: score => console.log(chalk.cyan(`⏱️ Performance audit complete: ${score}`))
  },
  tests: {
    start: () => console.log(chalk.blue(`🧪 Running test suite...`)),
    passed: count => console.log(chalk.green(`✅ ${count} tests passed`)),
    failed: count => console.log(chalk.red(`❌ ${count} tests failed`)),
    coverage: percent => console.log(chalk.cyan(`📊 Coverage: ${percent}`))
  },
  hooks: {
    preCommit: () => console.log(chalk.cyan(`🔄 Running pre-commit hooks...`)),
    lintPassed: () => console.log(chalk.green(`✅ Linting passed`)),
    typePassed: () => console.log(chalk.green(`✅ Type checking passed`)),
    ciStart: id => console.log(chalk.magenta(`🔄 CI pipeline started: Build #${id}`)),
    ciPassed: (id, time) => console.log(chalk.green(`✅ CI pipeline passed: Build #${id} (${time})`))
  },
  resources: {
    memory: percent => console.log(chalk.yellow(`⚠️ Memory usage high: ${percent}%`)),
    apiRate: (used, limit) => console.log(chalk.yellow(`⚠️ API rate limit at ${used}% (${used}/${limit} requests)`)),
    dbConnections: (active, total) => console.log(chalk.cyan(`📊 DB connections: ${active}/${total}`))
  }
};

module.exports = notify;

if (require.main === module) {
  notify.process.serverStarted(3000);
  notify.process.dbConnected();
  notify.process.webpackDone();

  notify.build.start();
  notify.build.complete('3.2s');

  notify.audit.start();
  notify.audit.critical(2);
  notify.audit.moderate(5);
  notify.audit.bundleWarn('1.2MB (limit: 1MB)');
  notify.audit.perf('85/100');

  notify.tests.start();
  notify.tests.passed(125);
  notify.tests.failed(3);
  notify.tests.coverage('87%');

  notify.hooks.preCommit();
  notify.hooks.lintPassed();
  notify.hooks.typePassed();
  notify.hooks.ciStart(452);
  notify.hooks.ciPassed(452, '3m 24s');

  notify.resources.memory(75);
  notify.resources.apiRate(80, 100);
  notify.resources.dbConnections(12, 20);
}
