<!DOCTYPE html>
<html>
<head>
    <title>TargetWise Load Debug</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        .loading { color: orange; }
    </style>
</head>
<body>
    <h1>TargetWise Loading Debug</h1>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        const startTime = performance.now();

        function log(message, status = 'loading') {
            const div = document.createElement('div');
            div.className = status;
            div.textContent = `[${((performance.now() - startTime) / 1000).toFixed(2)}s] ${message}`;
            results.appendChild(div);
        }

        async function checkResource(url, type) {
            log(`Loading ${type}: ${url}`);
            try {
                const start = performance.now();
                const response = await fetch(url);
                const time = performance.now() - start;
                
                if (response.ok) {
                    log(`✓ ${type} loaded in ${time.toFixed(0)}ms: ${url}`, 'success');
                    return { success: true, time };
                } else {
                    log(`✗ ${type} failed (${response.status}): ${url}`, 'error');
                    return { success: false, status: response.status };
                }
            } catch (error) {
                log(`✗ ${type} error: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        async function runDiagnostics() {
            log('Starting TargetWise load diagnostics...');
            
            // Check main resources
            const resources = [
                { url: '/index.html', type: 'HTML' },
                { url: '/src/styles/index.css', type: 'CSS' },
                { url: '/src/app.js', type: 'JavaScript' },
                { url: '/src/styles/design-system/variables.css', type: 'CSS Variables' },
                { url: '/src/styles/design-system/base.css', type: 'CSS Base' },
                { url: '/src/styles/design-system/components.css', type: 'CSS Components' },
                { url: '/src/styles/design-system/utilities.css', type: 'CSS Utilities' },
                { url: '/src/styles/targetwise-enhanced.css', type: 'CSS Enhanced' },
                { url: '/src/pages/TargetWiseMain.js', type: 'Page Module' },
                { url: '/src/pages/Dashboard.js', type: 'Dashboard Module' },
                { url: '/src/pages/InterestPool.js', type: 'Interest Pool Module' },
                { url: '/src/pages/InterestSuggestions.js', type: 'Suggestions Module' },
                { url: '/src/pages/InterestSearchEnhanced.js', type: 'Search Module' },
                { url: '/sw.js', type: 'Service Worker' },
                { url: '/manifest.json', type: 'PWA Manifest' }
            ];

            let totalTime = 0;
            let failures = 0;

            for (const resource of resources) {
                const result = await checkResource(resource.url, resource.type);
                if (result.success) {
                    totalTime += result.time;
                } else {
                    failures++;
                }
            }

            log(`\nTotal load time: ${totalTime.toFixed(0)}ms`, failures === 0 ? 'success' : 'error');
            log(`Resources loaded: ${resources.length - failures}/${resources.length}`, failures === 0 ? 'success' : 'error');

            // Check for module loading
            log('\nChecking module imports...');
            try {
                const appModule = await import('/src/app.js');
                log('✓ App module loaded successfully', 'success');
            } catch (error) {
                log(`✗ App module error: ${error.message}`, 'error');
            }
        }

        runDiagnostics();
    </script>
</body>
</html>