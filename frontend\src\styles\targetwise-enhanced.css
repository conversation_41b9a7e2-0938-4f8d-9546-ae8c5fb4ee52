/* TargetWise Enhanced Styles - Matching provided HTML designs exactly */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #f0f2f5;
    color: #1a1a2e;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
    transition: transform 0.2s;
}

.logo:hover {
    transform: translateY(-1px);
}

.logo svg {
    width: 32px;
    height: 32px;
}

.nav-menu {
    display: flex;
    gap: 8px;
    align-items: center;
}

.nav-link {
    padding: 8px 16px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s;
}

.nav-link:hover {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.08);
}

.nav-link.active {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: none;
    background: #f1f5f9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.theme-toggle:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 48px;
    font-weight: 800;
    color: white;
    margin-bottom: 16px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
}

.feature-icon {
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Background decoration */
.hero-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
}

.circle-1 {
    width: 400px;
    height: 400px;
    top: -200px;
    right: -100px;
}

.circle-2 {
    width: 300px;
    height: 300px;
    bottom: -150px;
    left: -50px;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: -60px auto 60px;
    padding: 0 24px;
    position: relative;
    z-index: 10;
}

/* Info Cards */
.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.info-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s;
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.info-card-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 24px;
}

.info-card:nth-child(2) .info-card-icon {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.info-card:nth-child(3) .info-card-icon {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.info-card-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

.info-card-text {
    color: #64748b;
    line-height: 1.6;
}

/* Builder Card */
.builder-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    padding: 48px;
    margin-bottom: 40px;
}

.builder-header {
    text-align: center;
    margin-bottom: 48px;
}

.builder-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.builder-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.builder-description {
    color: #64748b;
    font-size: 16px;
}

/* Form Sections */
.form-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-icon {
    width: 32px;
    height: 32px;
    background: #f0f9ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2563eb;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #e2e8f0;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
    background: #fafbfc;
}

.file-upload-area:hover {
    border-color: #2563eb;
    background: #f0f9ff;
}

.file-upload-area.active {
    border-color: #2563eb;
    background: #f0f9ff;
    border-style: solid;
}

.upload-icon {
    width: 48px;
    height: 48px;
    background: #e0f2fe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: #2563eb;
}

.upload-text {
    color: #475569;
    margin-bottom: 12px;
}

.upload-hint {
    color: #94a3b8;
    font-size: 14px;
}

.file-input {
    display: none;
}

.sample-csv-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
    margin-top: 16px;
    transition: all 0.2s;
}

.sample-csv-link:hover {
    gap: 12px;
    color: #1d4ed8;
}

/* Text Input */
.textarea-wrapper {
    position: relative;
}

.form-textarea {
    width: 100%;
    min-height: 120px;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-family: inherit;
    font-size: 16px;
    resize: vertical;
    transition: all 0.2s;
}

.form-textarea:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.char-counter {
    position: absolute;
    bottom: 12px;
    right: 12px;
    color: #94a3b8;
    font-size: 13px;
}

/* Input Grid */
.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: #475569;
}

.form-input {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Age Range */
.age-range {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 16px;
    align-items: end;
}

.range-separator {
    color: #94a3b8;
    padding-bottom: 12px;
}

/* Submit Button */
.submit-section {
    text-align: center;
    margin-top: 48px;
}

.btn-submit {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    padding: 16px 48px;
    border: none;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
    display: inline-flex;
    align-items: center;
    gap: 12px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.35);
}

.btn-submit:active {
    transform: translateY(0);
}

/* Success Message */
.success-message {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #10b981;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
    display: none;
    align-items: center;
    gap: 12px;
}

.success-icon {
    width: 24px;
    height: 24px;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.success-text {
    color: #059669;
    font-weight: 500;
}

/* Loading State */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.spinner {
    width: 48px;
    height: 48px;
    border: 3px solid #f3f4f6;
    border-top-color: #2563eb;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.loading-subtext {
    color: #64748b;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: white;
    border-radius: 16px;
    padding: 24px;
    height: fit-content;
    position: sticky;
    top: 100px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
}

.sidebar-title {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: #94a3b8;
    margin-bottom: 16px;
    letter-spacing: 0.5px;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: all 0.2s;
}

.sidebar-item:hover {
    background: #f8fafc;
    color: #1e293b;
    transform: translateX(4px);
}

.sidebar-item.active {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
}

.sidebar-icon {
    width: 20px;
    height: 20px;
    opacity: 0.7;
}

/* Main Content */
.main-content {
    flex: 1;
    min-width: 0;
}

/* Page Header */
.page-header {
    background: white;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
}

.page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.page-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.page-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.25);
}

.page-info h1 {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.page-subtitle {
    color: #64748b;
    font-size: 16px;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* Cards and Grids */
.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.info-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s;
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

.info-card-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.info-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.info-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 4px;
}

.stat-label {
    color: #64748b;
    font-size: 14px;
}

/* Form Elements */
.form-section {
    margin-bottom: 32px;
}

.form-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #475569;
    margin-bottom: 8px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.textarea-wrapper {
    position: relative;
}

.char-counter {
    position: absolute;
    bottom: 8px;
    right: 8px;
    font-size: 12px;
    color: #94a3b8;
}

.age-inputs {
    display: flex;
    align-items: center;
    gap: 12px;
}

.age-separator {
    color: #94a3b8;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
    padding: 48px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
    margin-bottom: 32px;
}

.file-upload-area:hover {
    border-color: #2563eb;
    background: rgba(37, 99, 235, 0.02);
}

.file-upload-area.drag-over {
    border-color: #2563eb;
    background: rgba(37, 99, 235, 0.05);
}

.upload-icon {
    width: 64px;
    height: 64px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 32px;
}

.upload-text {
    font-size: 16px;
    color: #1e293b;
    margin-bottom: 8px;
}

.upload-subtext {
    color: #64748b;
    font-size: 14px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
}

.btn-secondary {
    background: white;
    color: #475569;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.btn-submit {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    font-size: 18px;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.35);
}

.btn-submit:active {
    transform: translateY(0);
}

/* Tables */
.table-container {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th {
    background: #f8fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #475569;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e2e8f0;
}

.results-table td {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
}

.results-table tr:hover {
    background: #fafbfc;
}

/* Search Panel */
.search-panel {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-input-wrapper {
    position: relative;
    flex: 1;
    margin-right: 16px;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
}

.search-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Tab Container */
.tab-container {
    display: flex;
    gap: 4px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.tab {
    flex: 1;
    padding: 8px 16px;
    background: transparent;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
}

.tab:hover {
    color: #475569;
}

.tab.active {
    background: white;
    color: #2563eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 80px 24px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 40px;
}

.empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.empty-subtitle {
    color: #64748b;
}

/* Loading State */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #e2e8f0;
    border-top-color: #2563eb;
    border-radius: 50%;
    margin: 0 auto 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.loading-subtext {
    color: #64748b;
}

/* Success Message */
.success-message {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #86efac;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-icon {
    width: 32px;
    height: 32px;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.tool-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

.tool-card:hover::before {
    transform: scaleX(1);
}

.tool-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.tool-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.tool-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 16px;
}

.tool-stats {
    display: flex;
    gap: 24px;
    padding-top: 16px;
    border-top: 1px solid #f1f5f9;
}

.tool-stat {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.tool-stat-value {
    font-weight: 600;
    color: #1e293b;
}

.tool-stat-label {
    font-size: 12px;
    color: #94a3b8;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
    cursor: pointer;
    transition: all 0.3s;
    z-index: 100;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 32px rgba(37, 99, 235, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        display: none;
    }

    .hero-title {
        font-size: 36px;
    }

    .info-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .header-content {
        padding: 12px 16px;
    }

    .nav-menu {
        display: none;
    }

    .hero-section {
        padding: 40px 0;
    }

    .hero-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .container {
        padding: 20px 16px;
    }

    .page-header {
        padding: 20px;
    }

    .file-upload-area {
        padding: 32px 20px;
    }
}