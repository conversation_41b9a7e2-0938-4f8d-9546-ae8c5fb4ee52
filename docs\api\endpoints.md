# API Endpoints Documentation

## Overview

This document provides detailed information about the API endpoints available in the TargetWise application. These endpoints allow you to interact with the Facebook Marketing API to search for interests, get suggestions, browse the interest taxonomy, and more.

## Base URL

All API endpoints are relative to the base URL of the application. For example, if the application is running at `http://localhost:8000`, the full URL for the `/api/targeting/search` endpoint would be `http://localhost:8000/api/targeting/search`.

## Authentication

Currently, the API does not require authentication. However, the Facebook Marketing API credentials (Ad Account ID and Access Token) must be configured in the application settings or environment variables.

## Endpoints

### 1. Interest Search

#### `GET /api/targeting/search`

Search for interests based on keywords.

**Query Parameters:**
- `q` (required): Search query
- `type` (optional): Type of targeting entity (default: "adinterest")
- `limit` (optional): Maximum number of results to return (default: 1000)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "*************",
      "name": "Travel",
      "audience_size_lower_bound": 1000000,
      "audience_size_upper_bound": 2000000,
      "path": ["Category", "Subcategory"],
      "topic": "Topic",
      "description": "Description"
    }
  ]
}
```

### 2. Interest Suggestions

#### `GET /api/targeting/suggestions`

Get suggestions for related interests based on a seed interest.

**Query Parameters:**
- `interest_id` (required): Interest ID to get suggestions for
- `limit` (optional): Maximum number of results to return (default: 1000)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "*********9117",
      "name": "Hiking",
      "audience_size_lower_bound": 500000,
      "audience_size_upper_bound": 1000000,
      "path": ["Category", "Subcategory"],
      "topic": "Topic",
      "description": "Description"
    }
  ]
}
```

### 3. Interest Taxonomy

#### `GET /api/targeting/taxonomy`

Browse Facebook interest taxonomy using the /targetingbrowse endpoint.

**Query Parameters:**
- `parent_id` (optional): Parent category ID
- `limit_type` (optional): Type of results to retrieve (default: "interests")
- `locale` (optional): Locale for names/descriptions (default: "en_US")

**Response:**
```json
{
  "data": [
    {
      "id": "*********9118",
      "name": "Outdoor Activities",
      "type": "interests",
      "path": ["Category", "Subcategory"],
      "audience_size": 5000000,
      "description": "Description",
      "has_children": true
    }
  ]
}
```

### 4. Bulk Interest Search

#### `POST /api/targeting/bulk-search`

Bulk search for interests based on multiple keywords, processed in parallel batches with throttling and retry.

**Request Body:**
```json
{
  "keywords": ["Travel", "Hiking", "Photography"],
  "country_code": "US",
  "limit_per_keyword": 10
}
```

**Response:**
```json
{
  "results": [
    {
      "keyword": "Travel",
      "interests": [
        {
          "id": "*************",
          "name": "Travel",
          "audience_size_lower_bound": 1000000,
          "audience_size_upper_bound": 2000000,
          "path": ["Category", "Subcategory"],
          "topic": "Topic",
          "description": "Description"
        }
      ]
    },
    {
      "keyword": "Hiking",
      "interests": [
        // Interests for "Hiking"
      ]
    },
    {
      "keyword": "Photography",
      "interests": [
        // Interests for "Photography"
      ]
    }
  ],
  "total_interests_found": 25,
  "processing_time_ms": 1250
}
```

### 5. Bulk Interest Suggestions

#### `POST /api/targeting/bulk-suggestions`

Get suggestions for related interests based on multiple seed interests, processed in parallel batches with throttling and retry.

**Request Body:**
```json
{
  "interest_ids": ["*************", "*********9117", "*********9118"],
  "country_code": "US",
  "limit_per_seed": 10,
  "deduplicate": true
}
```

**Response:**
```json
{
  "results": [
    {
      "seed_id": "*************",
      "seed_name": "Travel",
      "suggestions": [
        {
          "id": "*********9119",
          "name": "Adventure Travel",
          "audience_size_lower_bound": 500000,
          "audience_size_upper_bound": 1000000,
          "path": ["Category", "Subcategory"],
          "topic": "Topic",
          "description": "Description"
        }
      ]
    },
    {
      "seed_id": "*********9117",
      "seed_name": "Hiking",
      "suggestions": [
        // Suggestions for "Hiking"
      ]
    },
    {
      "seed_id": "*********9118",
      "seed_name": "Photography",
      "suggestions": [
        // Suggestions for "Photography"
      ]
    }
  ],
  "deduplicated_suggestions": [
    {
      "interest": {
        "id": "*********9120",
        "name": "Outdoor Photography",
        "audience_size_lower_bound": 300000,
        "audience_size_upper_bound": 500000,
        "path": ["Category", "Subcategory"],
        "topic": "Topic",
        "description": "Description"
      },
      "seed_count": 2,
      "seed_ids": ["*********9117", "*********9118"]
    }
  ],
  "total_suggestions_found": 28,
  "processing_time_ms": 1500
}
```

### 6. Sample CSV Download

#### `GET /api/targeting/sample-csv`

Download a sample CSV file for bulk operations.

**Query Parameters:**
- `type` (optional): Type of sample CSV to download (default: "search")
  - Options: "search" for bulk interest search, "suggestions" for bulk interest suggestions

**Response:**

A CSV file with sample data.

## Saved Searches and Favorites

The search interface lets users save custom searches and mark interests as favorites.

- **Saved Searches**: Use the *Save Search* button on the search page to store the current query and search type. Saved searches are listed in the sidebar with buttons to rerun or delete them. Data is stored locally in the browser using `localStorage`.
- **Favorites**: Each interest result has a star icon. Click the star to toggle the interest as a favorite. Favorite interests are persisted in `localStorage` and shown in a dedicated sidebar list.

## Error Handling

All API endpoints return appropriate HTTP status codes and error messages in case of failure. Common error codes include:

- `400 Bad Request`: Invalid request parameters
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server-side error

Error responses have the following format:

```json
{
  "detail": "Error message"
}
```
