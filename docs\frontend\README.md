# TargetWise Design System

This document describes the style guidelines used throughout the TargetWise user interface. The design system is implemented with CSS custom properties and reusable classes located under `static/css/design-system`.

## Color Tokens

Color palettes are defined in `tokens.css` as CSS variables. The primary and secondary palettes provide brand colors while neutral and semantic palettes cover common UI states:

- `--color-primary-*` and `--color-secondary-*` range from 50 to 900 for light to dark shades.
- Neutral colors `--color-neutral-50` through `--color-neutral-900` form the base text and background colors.
- Semantic colors such as `--color-success-*`, `--color-warning-*`, `--color-danger-*`, and `--color-info-*` represent status states.

Use these tokens instead of hardcoded hex values to ensure consistency.

## Typography

Typography variables define font families, sizes, weights and line heights. Utility classes in `utilities.css` expose these values via classes like `tw-text-lg`, `tw-font-bold` and `tw-leading-tight`. Headings and paragraph styles in `index.css` rely on these tokens so that text scales predictably across the app.

## Component Classes

Reusable components are implemented in `components.css` and include:

- Buttons using classes such as `tw-btn`, `tw-btn-primary` and `tw-btn-outline-secondary`.
- Cards with `tw-card`, `tw-card-header`, `tw-card-body` and `tw-card-footer`.
- Form controls like `tw-form-control` and `tw-form-label`.
- Utility components for alerts and badges (`tw-alert-*`, `tw-badge-*`).

Combine these classes with spacing and typography utilities from `utilities.css` to build consistent layouts.

## Dark Mode and Theme Customization

The base template includes a theme toggle button that switches the `data-bs-theme` attribute on the `<html>` element between `light` and `dark`. The preference is saved in `localStorage` so the chosen theme persists across visits. The button uses `aria-label="Toggle theme"` for better accessibility.

To enable dark mode by default, set `data-bs-theme="dark"` on the `<html>` element or store `"dark"` under the `theme` key in `localStorage`.

Themes can be customized by overriding any of the CSS variables defined in `tokens.css`. Create a new stylesheet that sets your custom values and load it after the default design system CSS files.

