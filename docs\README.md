# Documentation Directory

## Purpose
This directory contains comprehensive documentation for the TargetWise application. It serves as the single source of truth for project information, following the Documentation-Driven Development (DDD) approach.

## Contents
- **api/**: API documentation
- **design-system/**: Design system documentation
- **maintenance/**: Maintenance procedures and logs
- **navigation-guide.md**: Project navigation guide
- **project-structure.md**: Project structure documentation
- **research/**: Research documentation

## Usage
Documentation should be referenced during development and updated as changes are made:

```bash
# View documentation
cat docs/project-structure.md

# Update documentation after making changes
vim docs/maintenance/log.md
```

## Notes
- Documentation follows the DDD workflow
- All features should have corresponding documentation
- Documentation should be updated after every code change
- Documentation serves as the single source of truth for the project
- Use Markdown format for all documentation files
