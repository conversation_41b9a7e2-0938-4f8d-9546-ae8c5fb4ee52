// tests/playwright/main-and-admin.spec.ts
import { test, expect } from '@playwright/test';

test.describe('TargetWise Main App and Admin Dashboard', () => {
  test('Main page loads and core UI is present', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle(/Facebook Algorithmic Targeting/i);
    await expect(page.getByRole('heading', { name: /Upload Seed Interests/i })).toBeVisible();
    await expect(page.getByRole('form', { name: /Upload Seed Interests/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Build Targeting Sheet/i })).toBeVisible();
    await expect(page.getByText(/Powered by Meta Marketing API/i)).toBeVisible();
  });

  test('Admin dashboard loads and credentials form is present', async ({ page }) => {
    await page.goto('/admin');
    await expect(page).toHaveTitle(/Admin Dashboard/i);
    await expect(page.getByRole('heading', { name: /Admin Dashboard/i })).toBeVisible();
    await expect(page.getByRole('form', { name: /Facebook API Credentials/i })).toBeVisible();
    await expect(page.getByRole('form', { name: /Application Settings/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Save Credentials/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Test Connection/i })).toBeVisible();
  });
});
