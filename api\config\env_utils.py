"""Utility functions for environment variable management."""
import os
from typing import Any, Optional
from pathlib import Path
from dotenv import load_dotenv

def load_environment(env_file: Optional[str | Path] = None) -> None:
    """Load environment variables from a file.
    
    Args:
        env_file: Path to the .env file. If None, looks for .env in the project root.
    """
    if env_file is None:
        # Look for .env in the project root (2 levels up from config/)
        env_file = Path(__file__).parent.parent / ".env"
    
    if isinstance(env_file, str):
        env_file = Path(env_file)
    
    if env_file.exists():
        load_dotenv(dotenv_path=env_file, override=True)

def get_env_variable(key: str, default: Any = None, required: bool = False) -> str:
    """Get an environment variable.
    
    Args:
        key: The environment variable name.
        default: Default value if the variable is not set.
        required: If True, raises an error if the variable is not set.
        
    Returns:
        The value of the environment variable or the default.
        
    Raises:
        ValueError: If the variable is required but not set.
    """
    value = os.getenv(key, default)
    if required and value is None:
        raise ValueError(f"Environment variable {key} is required but not set.")
    return value

def ensure_environment() -> None:
    """Ensure all required environment variables are set."""
    required_vars = [
        "SECRET_KEY",
        "FACEBOOK_APP_ID",
        "FACEBOOK_APP_SECRET",
        "FACEBOOK_ACCESS_TOKEN",
    ]
    
    missing = [var for var in required_vars if not os.getenv(var)]
    if missing:
        raise EnvironmentError(
            f"Missing required environment variables: {', '.join(missing)}"
        )

# Load environment variables when this module is imported
load_environment()

__all__ = ["load_environment", "get_env_variable", "ensure_environment"]
