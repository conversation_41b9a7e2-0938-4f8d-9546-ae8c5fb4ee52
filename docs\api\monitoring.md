# API Monitoring

## Overview

This document describes the API monitoring functionality implemented in the TargetWise application. The monitoring system tracks API calls, success rates, cache hit rates, response times, and error rates to help identify performance issues and optimize API usage.

## Implementation

The API monitoring functionality is implemented in the following files:

1. `app/utils/monitoring.py`: Contains the `APIMonitor` class that tracks API calls and provides statistics.
2. `app/utils/api_utils.py`: The `ApiRequestHandler` class has been updated to use the `APIMonitor` to track API calls.
3. `app/api/routes.py`: New endpoints have been added to view and reset API statistics.

## Monitoring Features

The API monitoring system tracks the following metrics:

1. **API Call Counts**:
   - Total API calls
   - Successful API calls
   - Failed API calls

2. **Cache Performance**:
   - Cache hits
   - Cache misses
   - Cache hit rate

3. **Response Times**:
   - Average response time
   - Response time distribution

4. **Error Tracking**:
   - Error counts by error type
   - Top errors

5. **Endpoint Usage**:
   - Endpoint call counts
   - Top endpoints

## API Endpoints

The following API endpoints are available for monitoring:

### Get API Statistics

```
GET /admin/api-stats
```

Returns statistics about API calls and cache performance.

Example response:
```json
{
  "success": true,
  "data": {
    "uptime_seconds": 3600,
    "uptime_formatted": "1:00:00",
    "api_calls": 1000,
    "api_successes": 950,
    "api_failures": 50,
    "success_rate": 95.0,
    "cache_hits": 700,
    "cache_misses": 300,
    "cache_hit_rate": 70.0,
    "avg_response_time": 0.25,
    "top_errors": [
      ["17:Rate limit exceeded", 30],
      ["4:Application request limit", 15],
      ["1:Unknown error", 5]
    ],
    "top_endpoints": [
      ["act_123456789/targetingsearch", 500],
      ["act_123456789/targetingsuggestions", 300],
      ["act_123456789/targetingbrowse", 200]
    ]
  }
}
```

### Reset API Statistics

```
POST /admin/reset-api-stats
```

Resets all API statistics.

Example response:
```json
{
  "success": true,
  "message": "API statistics reset successfully"
}
```

## Integration with ApiRequestHandler

The `ApiRequestHandler` class has been updated to use the `APIMonitor` to track API calls. The following events are tracked:

1. **Cache Hits**: When a request is served from the cache (Redis or local).
2. **API Calls**: When a request is made to the Facebook API.
3. **API Successes**: When an API call returns a successful response.
4. **API Failures**: When an API call returns an error response.
5. **Response Times**: The time taken to complete each request.

## Logging

In addition to in-memory tracking, the `APIMonitor` can also log API calls to a file. This is controlled by the `API_MONITOR_LOG_FILE` environment variable. If set, API calls will be logged to the specified file in JSON format.

Example log entry:
```json
{"timestamp": "2025-05-26T12:34:56.789012", "endpoint": "act_123456789/targetingsearch", "success": true, "response_time": 0.25, "cache_hit": false}
```

## Usage in Code

### Tracking API Calls

The `ApiRequestHandler` automatically tracks API calls using the `APIMonitor`. No additional code is needed to track API calls when using the `ApiRequestHandler`.

### Viewing API Statistics

To view API statistics, call the `/admin/api-stats` endpoint:

```python
import requests

response = requests.get("http://localhost:8000/admin/api-stats")
stats = response.json()["data"]
print(f"API calls: {stats['api_calls']}")
print(f"Success rate: {stats['success_rate']}%")
print(f"Cache hit rate: {stats['cache_hit_rate']}%")
```

### Resetting API Statistics

To reset API statistics, call the `/admin/reset-api-stats` endpoint:

```python
import requests

response = requests.post("http://localhost:8000/admin/reset-api-stats")
print(response.json()["message"])
```

## Future Improvements

1. **Real-time Monitoring**: Add WebSocket support for real-time monitoring of API calls.
2. **Alerting**: Add alerting for high error rates or low cache hit rates.
3. **Visualization**: Add a dashboard for visualizing API statistics.
4. **Detailed Logging**: Add more detailed logging for debugging purposes.
5. **Performance Optimization**: Use the monitoring data to optimize API usage and caching strategies.
