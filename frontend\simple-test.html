<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .loading { color: blue; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-card">
        <h1>JavaScript Module Loading Test</h1>
        <div id="status" class="loading">Testing module loading...</div>
        <div id="details"></div>
    </div>
    
    <script type="module">
        console.log('Module script starting...');
        
        try {
            // Test basic module functionality
            document.getElementById('status').innerHTML = '<span class="loading">Attempting to load app.js...</span>';
            
            // Try to import the main app module
            const appModule = await import('./src/app.js');
            console.log('App module loaded successfully:', appModule);
            
            document.getElementById('status').innerHTML = '<span class="success">✅ Module loaded successfully!</span>';
            document.getElementById('details').innerHTML = '<p>App module exports: ' + Object.keys(appModule).join(', ') + '</p>';
            
        } catch (error) {
            console.error('Module loading failed:', error);
            document.getElementById('status').innerHTML = '<span class="error">❌ Module loading failed</span>';
            document.getElementById('details').innerHTML = '<p>Error: ' + error.message + '</p><p>Stack: ' + error.stack + '</p>';
        }
    </script>
</body>
</html>
