# TargetWise Navigation Guide ✅ UPDATED

This document provides a comprehensive guide to navigating the TargetWise application, including the main pages, their purposes, and how they connect.

## Recent Updates (Project Cleanup)
- ✅ All pages updated to match enhanced HTML designs exactly
- ✅ Header navigation standardized across all pages
- ✅ Sidebar navigation improved with consistent icons and active states
- ✅ Removed 10 unused files for better performance
- ✅ All functionality verified and working correctly

## Main Navigation

The main navigation bar is located at the top of every page and varies by page type:

### Main Page Navigation
- **Home**: The landing page of the application
- **Micro-Tools**: Access to the micro-tools suite
- **Pricing**: Information about pricing plans
- **Docs**: Access to the application documentation

### Dashboard & Micro-Tools Navigation
- **Home**: The landing page of the application
- **Micro-Tools**: Access to the micro-tools suite
- **Documentation**: Access to the application documentation
- **Pricing**: Information about pricing plans
- **Admin**: Access to the admin dashboard

## Page Structure

### 1. Home Page (`/`)

The home page serves as the landing page for the TargetWise application. It provides:

- An overview of the application
- Quick access to key features
- Getting started information

### 2. Micro-Tools Dashboard (`/dashboard`)

The micro-tools dashboard is the central hub for accessing the specialized tools for working with Facebook targeting options. It includes:

- Welcome section with API counter
- Cards for each micro-tool with descriptions
- Quick links to each tool
- Floating action button for quick access

### 3. Interest Search (`/search`)

The Interest Search tool allows users to search for interests by keyword. Features include:

- Single interest search with tabs
- Search form with keyword input and type selection
- Results table with comprehensive data
- Search history tracking
- Copy functionality for interest names

#### Sidebar Navigation

The sidebar on the left provides navigation between micro-tools:

- Dashboard
- Interest Search
- Interest Suggestions
- Taxonomy Browser
- Interest Pool

#### Main Content

The main content area includes:

- Search form
- Results table
- Staging cart

### 4. Interest Suggestions (`/micro-tools/suggestions`)

The Interest Suggestions tool allows users to get suggestions for related interests. Features include:

- Single interest suggestions
- Bulk interest suggestions
- Results table with filtering
- Add to staging cart functionality
- Export to CSV

### 5. Taxonomy Browser (`/micro-tools/browse`)

The Taxonomy Browser tool allows users to browse the Facebook targeting taxonomy. Features include:

- Category selection
- Hierarchical category browser
- Interest list for selected category
- Add to staging cart functionality
- Export to CSV

### 6. Interest Pool (`/micro-tools/interest-pool`)

The Interest Pool tool allows users to manage a collection of interests. Features include:

- Table view of interests
- Filtering by name, audience size, type, and path
- Copy interest name functionality
- Remove from pool functionality
- Export to CSV
- Clear all functionality

### 7. Admin Dashboard (`/admin`)

The Admin Dashboard allows administrators to manage the application. Features include:

- Facebook API credentials management
- Test connection functionality
- Browse targeting categories
- Application settings

## Common UI Elements

### 1. Staging Cart

The staging cart appears on the Interest Search, Interest Suggestions, and Taxonomy Browser pages. It allows users to:

- Collect interests from different sources
- Review selected interests
- Create an interest pool from the staging cart
- Clear the staging cart

### 2. Results Table

The results table appears on all micro-tools pages. It includes:

- Interest name
- Interest ID
- Audience size
- Type
- Path
- Actions (Add to cart, Copy, etc.)

### 3. Filters

Filters are available on all results tables and allow users to filter by:

- Interest name
- Audience size
- Type
- Path

### 4. Export to CSV

The Export to CSV button is available on all results tables and allows users to export the current results to a CSV file.

## Navigation Flow

### Typical User Flow

1. User starts at the Home page
2. User navigates to the Micro-Tools Dashboard
3. User selects a specific micro-tool (e.g., Interest Search)
4. User performs actions within the tool (search, filter, add to cart)
5. User navigates to another micro-tool using the sidebar
6. User creates an interest pool from the staging cart
7. User navigates to the Interest Pool page to manage interests

### Admin Flow

1. Admin navigates to the Admin Dashboard
2. Admin manages Facebook API credentials
3. Admin tests the connection to the Facebook API
4. Admin browses targeting categories
5. Admin adjusts application settings

## Mobile Navigation

On mobile devices, the navigation adapts as follows:

- The main navigation collapses into a hamburger menu
- The sidebar in micro-tools pages is hidden by default and can be toggled
- The layout adjusts to fit smaller screens

## Keyboard Shortcuts

The application supports the following keyboard shortcuts:

- `Alt + S`: Focus the search input
- `Alt + F`: Focus the filters
- `Alt + C`: Focus the staging cart
- `Alt + E`: Export to CSV
- `Alt + 1-5`: Navigate to micro-tools (1: Dashboard, 2: Search, etc.)

## URL Structure ✅ UPDATED

The application uses a consistent URL structure:

- `/`: Home page
- `/dashboard`: Micro-tools dashboard
- `/search`: Interest Search
- `/suggestions`: Interest Suggestions
- `/taxonomy`: Taxonomy Browser
- `/pool`: Interest Pool
- `/admin`: Admin Dashboard (backend)
- `/api/v1/...`: API endpoints

### Frontend Server
- **URL**: http://localhost:8080/
- **Server**: Python HTTP server (`frontend/server.py`)
- **Router**: Single-page application with client-side routing

### Backend API
- **URL**: http://localhost:8000/
- **Server**: FastAPI application (`run.py`)
- **Admin**: http://localhost:8000/admin

## Error Pages

The application includes the following error pages:

- `404 Not Found`: Displayed when a page is not found
- `500 Internal Server Error`: Displayed when an internal error occurs
- `403 Forbidden`: Displayed when access is denied

Each error page includes:

- A clear error message
- A link to return to the home page
- Contact information for support
