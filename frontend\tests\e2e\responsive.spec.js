// DOC: docs/micro-tools/search-bulk-feature.md
const { test, expect } = require('@playwright/test');

test.describe('Mobile Responsive Behavior', () => {
  const breakpoints = [
    { width: 375, height: 800 },
    { width: 768, height: 1000 },
    { width: 1024, height: 1200 },
  ];

  for (const { width, height } of breakpoints) {
    test(`Viewport ${width}x${height}`, async ({ page }) => {
      await page.setViewportSize({ width, height });
      await page.goto('/micro-tools/search');
      const toggleVisible = await page.isVisible('#sidebarToggle');
      if (width < 768) {
        expect(toggleVisible).toBeTruthy();
        await page.click('#sidebarToggle');
        await expect(page.locator('.mt-search__sidebar')).toHaveClass(/open/);
      } else {
        expect(toggleVisible).toBeFalsy();
      }
    });
  }
});