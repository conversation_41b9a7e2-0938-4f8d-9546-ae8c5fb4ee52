/**
 * TargetWise Notification System
 * Provides consistent notifications across both terminal and web interfaces
 * @see docs/micro-tools/notification-system.md
 */

class NotificationSystem {
  constructor() {
    this.container = null;
    this.notificationCount = 0;
    this.maxNotifications = 5;
    this.init();
  }

  init() {
    // Create container if it doesn't exist
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.className = 'notification-container';
      this.container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 450px;
        width: 100%;
      `;
      document.body.appendChild(this.container);
      
      // Add styles for notifications
      const style = document.createElement('style');
      style.textContent = `
        .notification {
          margin-bottom: 10px;
          padding: 15px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          display: flex;
          align-items: center;
          opacity: 0;
          transform: translateX(50px);
          transition: opacity 0.3s ease, transform 0.3s ease;
          background: white;
          border-left: 5px solid #ccc;
          position: relative;
          overflow: hidden;
        }
        
        .notification.visible {
          opacity: 1;
          transform: translateX(0);
        }
        
        .notification.success { border-left-color: #43a047; }
        .notification.error { border-left-color: #e53935; }
        .notification.warning { border-left-color: #ffa000; }
        .notification.info { border-left-color: #1e88e5; }
        .notification.debug { border-left-color: #9e9e9e; }
        
        .notification-icon {
          margin-right: 15px;
          font-size: 24px;
        }
        
        .notification-content {
          flex: 1;
        }
        
        .notification-title {
          font-weight: bold;
          margin-bottom: 5px;
          display: flex;
          justify-content: space-between;
        }
        
        .notification-message {
          margin: 0;
          color: #333;
        }
        
        .notification-close {
          cursor: pointer;
          padding: 5px;
          margin-left: 10px;
          opacity: 0.7;
        }
        
        .notification-close:hover {
          opacity: 1;
        }
        
        .notification-progress {
          position: absolute;
          bottom: 0;
          left: 0;
          height: 3px;
          background: rgba(0, 0, 0, 0.2);
          width: 100%;
        }
        
        @keyframes progress {
          from { width: 100%; }
          to { width: 0%; }
        }
      `;
      document.head.appendChild(style);
    }
  }
  
  /**
   * Show a notification in the browser
   * @param {string} type - Type of notification (success, error, warning, info, debug)
   * @param {string} message - Message to display
   * @param {Object} options - Additional options
   * @param {string} options.title - Title for the notification
   * @param {string} options.context - Additional context
   * @param {number} options.duration - Duration in ms (default: 5000, 0 for permanent)
   */
  notify(type, message, options = {}) {
    // Set defaults
    const types = {
      success: { icon: '✅', title: 'Success' },
      error: { icon: '❌', title: 'Error' },
      warning: { icon: '⚠️', title: 'Warning' },
      info: { icon: 'ℹ️', title: 'Information' },
      debug: { icon: '🐛', title: 'Debug' }
    };
    
    // Get the correct type or default to info
    const notificationType = types[type] ? type : 'info';
    const typeInfo = types[notificationType];
    
    // Prepare the notification
    const title = options.title || typeInfo.title;
    const duration = options.duration !== undefined ? options.duration : 5000;
    const context = options.context ? `[${options.context}]` : '';
    
    // Create the notification element
    const notification = document.createElement('div');
    notification.className = `notification ${notificationType}`;
    notification.innerHTML = `
      <div class="notification-icon">${typeInfo.icon}</div>
      <div class="notification-content">
        <div class="notification-title">
          ${title} ${context ? `<span style="opacity:0.7;font-size:0.9em;">${context}</span>` : ''}
          <span class="notification-close">×</span>
        </div>
        <p class="notification-message">${message}</p>
      </div>
      ${duration ? '<div class="notification-progress"></div>' : ''}
    `;
    
    // Manage notification count
    this.notificationCount++;
    if (this.notificationCount > this.maxNotifications) {
      const firstNotification = this.container.firstChild;
      if (firstNotification) {
        this.container.removeChild(firstNotification);
        this.notificationCount--;
      }
    }
    
    // Add the notification to the DOM
    this.container.appendChild(notification);
    
    // Setup progress animation if it has a duration
    if (duration) {
      const progress = notification.querySelector('.notification-progress');
      progress.style.animation = `progress ${duration/1000}s linear forwards`;
    }
    
    // Make notification visible with animation
    setTimeout(() => notification.classList.add('visible'), 10);
    
    // Setup close button
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => this.closeNotification(notification));
    
    // Auto-remove after duration (if not permanent)
    if (duration) {
      setTimeout(() => this.closeNotification(notification), duration);
    }
    
    // Log to console as well
    console[type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'log'](
      `%c${title}${context ? ` ${context}` : ''}: %c${message}`,
      `font-weight: bold; color: ${
        type === 'success' ? 'green' :
        type === 'error' ? 'red' :
        type === 'warning' ? 'orange' :
        type === 'info' ? 'blue' : 'gray'
      }`,
      'font-weight: normal; color: inherit'
    );
    
    return notification;
  }
  
  closeNotification(notification) {
    if (notification.classList.contains('closing')) return;
    
    notification.classList.add('closing');
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(50px)';
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
        this.notificationCount--;
      }
    }, 300);
  }
  
  // Helper methods
  success(message, options) { return this.notify('success', message, options); }
  error(message, options) { return this.notify('error', message, options); }
  warning(message, options) { return this.notify('warning', message, options); }
  info(message, options) { return this.notify('info', message, options); }
  debug(message, options) { return this.notify('debug', message, options); }
}

// Initialize and expose globally
window.notificationSystem = new NotificationSystem();

// Add convenient global methods
window.notify = {
  success: (message, options) => window.notificationSystem.success(message, options),
  error: (message, options) => window.notificationSystem.error(message, options),
  warning: (message, options) => window.notificationSystem.warning(message, options),
  info: (message, options) => window.notificationSystem.info(message, options),
  debug: (message, options) => window.notificationSystem.debug(message, options)
};

// Export for module usage
export default window.notificationSystem;
