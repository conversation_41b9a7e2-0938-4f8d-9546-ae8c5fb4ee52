/**
 * TargetWise Main Page - Algorithmic Targeting Sheet Builder
 * Matches the exact structure from targetwise-main-enhanced.html
 */

export function TargetWiseMainPage() {
  return `
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="/" class="logo">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <circle cx="12" cy="12" r="6"></circle>
                        <circle cx="12" cy="12" r="2"></circle>
                    </svg>
                    TargetWise
                </a>

                <nav class="nav-menu">
                    <a href="/" class="nav-link active">Home</a>
                    <a href="/dashboard" class="nav-link">Micro-Tools</a>
                    <a href="/pricing" class="nav-link">Pricing</a>
                    <a href="/docs" class="nav-link">Docs</a>
                </nav>

                <div class="header-actions">
                    <button class="theme-toggle">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"></circle>
                            <line x1="12" y1="1" x2="12" y2="3"></line>
                            <line x1="12" y1="21" x2="12" y2="23"></line>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                            <line x1="1" y1="12" x2="3" y2="12"></line>
                            <line x1="21" y1="12" x2="23" y2="12"></line>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-decoration">
                <div class="decoration-circle circle-1"></div>
                <div class="decoration-circle circle-2"></div>
            </div>
            <div class="hero-content">
                <h1 class="hero-title">Welcome to TargetWise</h1>
                <p class="hero-subtitle">Build powerful 12-column Algorithmic Targeting Sheets using the Meta Marketing API for optimized Facebook ad campaigns</p>
                <div class="hero-features">
                    <div class="hero-feature">
                        <div class="feature-icon">✓</div>
                        <span>AI-Powered Targeting</span>
                    </div>
                    <div class="hero-feature">
                        <div class="feature-icon">✓</div>
                        <span>Meta API Integration</span>
                    </div>
                    <div class="hero-feature">
                        <div class="feature-icon">✓</div>
                        <span>Export Ready</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Info Cards -->
            <div class="info-cards">
                <div class="info-card">
                    <div class="info-card-icon">🎯</div>
                    <h3 class="info-card-title">Smart Targeting</h3>
                    <p class="info-card-text">Generate precise audience segments using advanced algorithms and Meta's comprehensive interest database</p>
                </div>
                <div class="info-card">
                    <div class="info-card-icon">📊</div>
                    <h3 class="info-card-title">12-Column Format</h3>
                    <p class="info-card-text">Industry-standard targeting sheet format optimized for Facebook Ads Manager import and campaign setup</p>
                </div>
                <div class="info-card">
                    <div class="info-card-icon">⚡</div>
                    <h3 class="info-card-title">Instant Results</h3>
                    <p class="info-card-text">Get your targeting sheet in seconds, ready to use in your Facebook advertising campaigns</p>
                </div>
            </div>

            <!-- Builder Card -->
            <div class="builder-card">
                <div class="builder-header">
                    <h2 class="builder-title">
                        <div class="builder-icon">🚀</div>
                        Build Your Targeting Sheet
                    </h2>
                    <p class="builder-description">Upload your interests or enter them manually to generate a comprehensive targeting strategy</p>
                </div>

                <div class="success-message" id="successMessage">
                    <div class="success-icon">✓</div>
                    <span class="success-text">Targeting sheet generated successfully!</span>
                </div>

                <form id="targetingForm">
                    <!-- Upload Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                            </div>
                            Upload Interests CSV (Optional)
                        </h3>
                        <div class="file-upload-area" id="fileUploadArea">
                            <input type="file" id="fileInput" class="file-input" accept=".csv">
                            <div class="upload-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10 9 9 9 8 9"></polyline>
                                </svg>
                            </div>
                            <p class="upload-text">Click to upload or drag and drop</p>
                            <p class="upload-hint">CSV files only (max 10MB)</p>
                        </div>
                        <a href="#" class="sample-csv-link">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Download Sample CSV
                        </a>
                    </div>

                    <!-- Manual Input Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                            </div>
                            Seed Interests (Optional if CSV uploaded)
                        </h3>
                        <div class="textarea-wrapper">
                            <textarea
                                class="form-textarea"
                                placeholder="Enter comma-separated interests (e.g., fitness, yoga, meditation, wellness, health)..."
                                id="seedInterests"
                            ></textarea>
                            <span class="char-counter">0 interests</span>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M12 1v6m0 6v6m-9-9h6m6 0h6m-11.364 6.364l4.243-4.243m0 0l4.242-4.242m-4.242 4.242l-4.243-4.243m4.243 4.243l4.242 4.242"></path>
                                </svg>
                            </div>
                            Targeting Parameters
                        </h3>
                        <div class="input-grid">
                            <div class="form-group">
                                <label class="form-label">Country Code</label>
                                <input type="text" class="form-input" value="US" placeholder="e.g., US" id="countryCode">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Age Range</label>
                                <div class="age-range">
                                    <input type="number" class="form-input" value="18" placeholder="Min" min="13" max="65" id="minAge">
                                    <span class="range-separator">to</span>
                                    <input type="number" class="form-input" value="65" placeholder="Max" min="13" max="65" id="maxAge">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="submit-section">
                        <button type="submit" class="btn-submit">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 2L11 13"></path>
                                <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                            </svg>
                            Build Targeting Sheet
                        </button>
                    </div>
                </form>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p class="loading-text">Building Your Targeting Sheet</p>
                <p class="loading-subtext">This may take a few seconds...</p>
            </div>
        </div>
  `;
}

export function initTargetWiseMainPage() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const seedInterests = document.getElementById('seedInterests');
    const charCounter = document.querySelector('.char-counter');
    const form = document.getElementById('targetingForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const successMessage = document.getElementById('successMessage');

    // File upload handling
    if (fileUploadArea && fileInput) {
        fileUploadArea.addEventListener('click', () => fileInput.click());

        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('active');
        });

        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('active');
        });

        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('active');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'text/csv') {
                fileInput.files = files;
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        });
    }

    // Interest counter
    if (seedInterests && charCounter) {
        seedInterests.addEventListener('input', () => {
            const interests = seedInterests.value.split(',').filter(i => i.trim()).length;
            charCounter.textContent = `${interests} interests`;
        });
    }

    // Form submission
    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Validation
            const hasFile = fileInput.files.length > 0;
            const hasInterests = seedInterests.value.trim().length > 0;

            if (!hasFile && !hasInterests) {
                alert('Please upload a CSV file or enter seed interests');
                return;
            }

            // Show loading
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            // Simulate API call
            setTimeout(() => {
                // Hide loading
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }

                // Show success message
                if (successMessage) {
                    successMessage.style.display = 'flex';
                    setTimeout(() => {
                        successMessage.style.display = 'none';
                    }, 5000);
                }
            }, 3000);
        });
    }

    // Sample CSV download
    const sampleLink = document.querySelector('.sample-csv-link');
    if (sampleLink) {
        sampleLink.addEventListener('click', (e) => {
            e.preventDefault();
            downloadSampleCSV();
        });
    }

    function handleFileUpload(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            // Parse CSV and populate textarea
            const interests = content.split('\n')
                .map(line => line.trim())
                .filter(line => line)
                .join(', ');

            if (seedInterests) {
                seedInterests.value = interests;
                seedInterests.dispatchEvent(new Event('input'));
            }

            // Update upload area text
            const uploadText = fileUploadArea.querySelector('.upload-text');
            if (uploadText) {
                uploadText.textContent = `Selected: ${file.name}`;
                fileUploadArea.classList.add('active');
            }
        };
        reader.readAsText(file);
    }

    function downloadSampleCSV() {
        const csvContent = `Interest Name
Fitness
Yoga
Healthy Living
Meditation
Running
CrossFit
Nutrition`;

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sample_interests.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}