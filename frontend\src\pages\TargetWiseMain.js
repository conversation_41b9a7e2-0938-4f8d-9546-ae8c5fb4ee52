/**
 * TargetWise Main Page - Algorithmic Targeting Sheet Builder
 * Matches the exact structure from targetwise-main-enhanced.html
 */

export function TargetWiseMainPage() {
  return `
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="/" class="logo">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    TargetWise
                </a>
                
                <nav class="nav-menu">
                    <a href="/" class="nav-link active">Home</a>
                    <a href="/dashboard" class="nav-link">Dashboard</a>
                    <a href="/search" class="nav-link">Search</a>
                    <a href="/suggestions" class="nav-link">Suggestions</a>
                    <a href="/pool" class="nav-link">Interest Pool</a>
                </nav>
                
                <div class="header-actions">
                    <button class="theme-toggle">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Build Perfect Targeting Sheets in Seconds</h1>
                <p class="hero-subtitle">
                    Use our AI-powered algorithm to generate comprehensive 12-column targeting sheets 
                    that maximize your Facebook ad performance
                </p>
                <div class="hero-features">
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>AI-Powered Analysis</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>12-Column Format</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>Instant Results</span>
                    </div>
                </div>
            </div>
            <div class="hero-circle-1"></div>
            <div class="hero-circle-2"></div>
        </section>

        <!-- Main Content -->
        <div class="container">
            <!-- Info Cards -->
            <div class="info-cards">
                <div class="info-card">
                    <div class="info-card-icon">📊</div>
                    <h3 class="info-card-title">Smart Algorithm</h3>
                    <p class="info-card-text">
                        Our advanced algorithm analyzes your seed interests and generates optimal targeting combinations
                    </p>
                </div>
                <div class="info-card">
                    <div class="info-card-icon">🎯</div>
                    <h3 class="info-card-title">Precision Targeting</h3>
                    <p class="info-card-text">
                        Get highly relevant interests organized in a 12-column format ready for Facebook Ads Manager
                    </p>
                </div>
                <div class="info-card">
                    <div class="info-card-icon">⚡</div>
                    <h3 class="info-card-title">Instant Export</h3>
                    <p class="info-card-text">
                        Download your targeting sheet as CSV or copy directly to clipboard for immediate use
                    </p>
                </div>
            </div>

            <!-- Builder Card -->
            <div class="builder-card">
                <div class="builder-header">
                    <h2 class="builder-title">
                        <div class="builder-icon">🎯</div>
                        Create Your Targeting Sheet
                    </h2>
                    <p class="builder-description">
                        Upload a CSV file or enter your seed interests manually to generate a comprehensive targeting sheet
                    </p>
                </div>

                <form id="targeting-form">
                    <!-- File Upload Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">📁</div>
                            Upload Seed Interests
                        </h3>
                        <div class="file-upload-area" id="fileUploadArea">
                            <input type="file" id="fileInput" class="file-input" accept=".csv">
                            <div class="upload-icon">📤</div>
                            <p class="upload-text">Drag & drop your CSV file here</p>
                            <p class="upload-hint">or click to browse</p>
                        </div>
                        <a href="#" class="sample-csv-link">
                            Download sample CSV format
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
                            </svg>
                        </a>
                    </div>

                    <!-- Manual Entry Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">✏️</div>
                            Or Enter Manually
                        </h3>
                        <div class="textarea-wrapper">
                            <textarea 
                                id="seedInterests" 
                                class="form-textarea" 
                                placeholder="Enter seed interests separated by commas (e.g., Fitness, Yoga, Healthy Living)"
                            ></textarea>
                            <span class="char-counter">0 / 500</span>
                        </div>
                    </div>

                    <!-- Targeting Options -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">⚙️</div>
                            Targeting Options
                        </h3>
                        <div class="input-grid">
                            <div class="form-group">
                                <label class="form-label">Country</label>
                                <input type="text" class="form-input" placeholder="e.g., United States" value="United States">
                            </div>
                            <div class="age-range">
                                <div class="form-group">
                                    <label class="form-label">Min Age</label>
                                    <input type="number" class="form-input" min="13" max="65" value="18">
                                </div>
                                <span class="range-separator">-</span>
                                <div class="form-group">
                                    <label class="form-label">Max Age</label>
                                    <input type="number" class="form-input" min="13" max="65" value="65">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="submit-section">
                        <button type="submit" class="btn-submit">
                            Generate Targeting Sheet
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"/>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Success Message (hidden by default) -->
        <div class="success-message" id="successMessage" style="display: none;">
            <div class="success-icon">✓</div>
            <div>
                <strong>Success!</strong> Your targeting sheet has been generated successfully.
            </div>
        </div>

        <!-- Loading Overlay (hidden by default) -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p class="loading-text">Generating your targeting sheet...</p>
                <p class="loading-subtext">This may take a few moments</p>
            </div>
        </div>
  `;
}

export function initTargetWiseMainPage() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const seedInterests = document.getElementById('seedInterests');
    const charCounter = document.querySelector('.char-counter');
    const form = document.getElementById('targeting-form');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const successMessage = document.getElementById('successMessage');

    // File upload handling
    if (fileUploadArea && fileInput) {
        fileUploadArea.addEventListener('click', () => fileInput.click());
        
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('active');
        });
        
        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('active');
        });
        
        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('active');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'text/csv') {
                fileInput.files = files;
                handleFileUpload(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        });
    }

    // Character counter
    if (seedInterests && charCounter) {
        seedInterests.addEventListener('input', () => {
            const length = seedInterests.value.length;
            charCounter.textContent = `${length} / 500`;
            
            if (length > 500) {
                seedInterests.value = seedInterests.value.substring(0, 500);
                charCounter.textContent = '500 / 500';
            }
        });
    }

    // Form submission
    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Show loading
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
            
            // Simulate API call
            setTimeout(() => {
                // Hide loading
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
                
                // Show success message
                if (successMessage) {
                    successMessage.style.display = 'flex';
                    setTimeout(() => {
                        successMessage.style.display = 'none';
                    }, 5000);
                }
            }, 2000);
        });
    }

    // Sample CSV download
    const sampleLink = document.querySelector('.sample-csv-link');
    if (sampleLink) {
        sampleLink.addEventListener('click', (e) => {
            e.preventDefault();
            downloadSampleCSV();
        });
    }

    function handleFileUpload(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            // Parse CSV and populate textarea
            const interests = content.split('\n')
                .map(line => line.trim())
                .filter(line => line)
                .join(', ');
            
            if (seedInterests) {
                seedInterests.value = interests;
                seedInterests.dispatchEvent(new Event('input'));
            }
        };
        reader.readAsText(file);
    }

    function downloadSampleCSV() {
        const csvContent = `Interest Name
Fitness
Yoga
Healthy Living
Meditation
Running
CrossFit
Nutrition`;
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sample_interests.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}