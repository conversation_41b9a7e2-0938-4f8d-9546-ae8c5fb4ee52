<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interest Search - TargetWise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f0f2f5;
            color: #1a1a2e;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .logo:hover {
            transform: translateY(-1px);
        }

        .logo svg {
            width: 32px;
            height: 32px;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .nav-link {
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .nav-link:hover {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.08);
        }

        .nav-link.active {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .theme-toggle {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: none;
            background: #f1f5f9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: #e2e8f0;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline {
            background: transparent;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        .btn-outline:hover {
            border-color: #cbd5e1;
            background: #f8fafc;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
            box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
        }

        /* Main Layout */
        .main-container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            gap: 24px;
            padding: 24px;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 10px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: all 0.2s;
        }

        .sidebar-item:hover {
            background: #f1f5f9;
            color: #1e293b;
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            color: #2563eb;
        }

        .sidebar-icon {
            width: 20px;
            height: 20px;
            opacity: 0.7;
        }

        /* Content Area */
        .content {
            flex: 1;
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .page-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-left: 64px;
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        /* Search Panel */
        .search-panel {
            background: white;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .search-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .search-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .tab-container {
            display: flex;
            gap: 8px;
            background: #f1f5f9;
            padding: 4px;
            border-radius: 10px;
        }

        .tab {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            background: transparent;
            color: #64748b;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tab.active {
            background: white;
            color: #2563eb;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .search-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
        }

        .search-input-wrapper {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 14px 20px 14px 48px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
        }

        .input-hint {
            font-size: 13px;
            color: #94a3b8;
            margin-top: 4px;
        }

        .select-wrapper {
            position: relative;
        }

        .form-select {
            width: 100%;
            padding: 14px 20px;
            padding-right: 48px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            appearance: none;
            transition: all 0.2s;
        }

        .form-select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .select-arrow {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            color: #94a3b8;
        }

        .search-actions {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .btn-search {
            flex: 1;
            padding: 14px 24px;
            font-size: 16px;
        }

        .btn-save {
            background: white;
            color: #2563eb;
            border: 2px solid #2563eb;
        }

        .btn-save:hover {
            background: rgba(37, 99, 235, 0.05);
        }

        /* Results Section */
        .results-section {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .results-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .results-badge {
            background: #2563eb;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
        }

        .results-actions {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }

        /* Table Styles */
        .results-table {
            width: 100%;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            text-align: left;
            padding: 16px;
            font-weight: 600;
            color: #475569;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #f1f5f9;
            background: #fafbfc;
        }

        td {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
        }

        tr:hover {
            background: #fafbfc;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
        }

        .checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
            accent-color: #2563eb;
        }

        .favorite-btn {
            width: 24px;
            height: 24px;
            border: none;
            background: none;
            cursor: pointer;
            color: #cbd5e1;
            transition: all 0.2s;
        }

        .favorite-btn:hover {
            color: #fbbf24;
            transform: scale(1.2);
        }

        .favorite-btn.active {
            color: #fbbf24;
        }

        .interest-name {
            font-weight: 500;
            color: #1e293b;
        }

        .interest-id {
            font-family: monospace;
            color: #64748b;
            font-size: 13px;
        }

        .audience-size {
            font-weight: 500;
            color: #059669;
        }

        .interest-type {
            display: inline-block;
            padding: 4px 12px;
            background: #e0f2fe;
            color: #0369a1;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        .interest-path {
            color: #64748b;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn:hover {
            border-color: #2563eb;
            color: #2563eb;
            background: rgba(37, 99, 235, 0.05);
        }

        /* Search History */
        .history-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-top: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .history-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .history-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .history-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            border-radius: 8px;
            background: #f8fafc;
            transition: all 0.2s;
            cursor: pointer;
        }

        .history-item:hover {
            background: #f1f5f9;
            transform: translateX(4px);
        }

        .history-term {
            color: #475569;
            font-weight: 500;
        }

        .history-time {
            color: #94a3b8;
            font-size: 13px;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            background: #f1f5f9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 36px;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .empty-text {
            color: #64748b;
        }

        /* Loading State */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f4f6;
            border-top-color: #2563eb;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
        }

        @media (max-width: 640px) {
            .header-content {
                padding: 12px 16px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .page-header {
                padding: 24px 16px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .search-panel,
            .results-section,
            .history-section {
                padding: 24px 16px;
            }
            
            .results-table {
                font-size: 14px;
            }
            
            th, td {
                padding: 12px 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <circle cx="12" cy="12" r="6"></circle>
                    <circle cx="12" cy="12" r="2"></circle>
                </svg>
                TargetWise
            </a>
            
            <nav class="nav-menu">
                <a href="#" class="nav-link">Home</a>
                <a href="#" class="nav-link active">Micro-Tools</a>
                <a href="#" class="nav-link">Documentation</a>
                <a href="#" class="nav-link">Pricing</a>
                <a href="#" class="nav-link">Admin</a>
            </nav>
            
            <div class="header-actions">
                <button class="theme-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
                <a href="#" class="btn btn-outline">Log In</a>
                <a href="#" class="btn btn-primary">Sign Up Free</a>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3 class="sidebar-title">Micro-Tools</h3>
            <nav class="sidebar-menu">
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    Dashboard
                </a>
                <a href="#" class="sidebar-item active">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    Interest Search
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                    </svg>
                    Interest Suggestions
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 3v18h18"></path>
                        <path d="m19 9-5 5-4-4-3 3"></path>
                    </svg>
                    Taxonomy Browser
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                    </svg>
                    Interest Pool
                </a>
            </nav>
        </aside>

        <!-- Content -->
        <main class="content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-header-top">
                    <div>
                        <h1 class="page-title">
                            <div class="page-icon">🔍</div>
                            Interest Search
                        </h1>
                        <p class="page-subtitle">Search for Facebook interests and add them to your pool</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Back to Tools
                        </button>
                        <button class="btn btn-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                                <line x1="4" y1="22" x2="4" y2="15"></line>
                            </svg>
                            Main App
                        </button>
                    </div>
                </div>
            </div>

            <!-- Search Panel -->
            <div class="search-panel">
                <div class="search-header">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    <h2 class="search-title">Find Interests</h2>
                    <div class="tab-container">
                        <button class="tab active" id="singleTab">Single Search</button>
                        <button class="tab" id="bulkTab">Bulk Search</button>
                    </div>
                </div>

                <form class="search-form">
                    <div class="form-group">
                        <label class="form-label">Search Keywords</label>
                        <div class="search-input-wrapper">
                            <svg class="search-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            <input type="text" class="search-input" placeholder="Enter a keyword like 'hiking', 'movies', or 'technology'">
                        </div>
                        <p class="input-hint">Tip: Use specific keywords to find targeted audience interests</p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Search Type</label>
                        <div class="select-wrapper">
                            <select class="form-select">
                                <option>Interests</option>
                                <option>Behaviors</option>
                                <option>Demographics</option>
                                <option>Job Titles</option>
                                <option>Employers</option>
                            </select>
                            <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                    </div>

                    <div class="search-actions">
                        <button type="submit" class="btn btn-primary btn-search">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            Search
                        </button>
                        <button type="button" class="btn btn-save">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                <polyline points="7 3 7 8 15 8"></polyline>
                            </svg>
                            Save Search
                        </button>
                    </div>
                </form>
            </div>

            <!-- Results Section -->
            <div class="results-section">
                <div class="results-header">
                    <h3 class="results-title">
                        Results
                        <span class="results-badge">0</span>
                    </h3>
                    <div class="results-actions">
                        <button class="btn btn-icon btn-secondary" title="Select All">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <polyline points="9 11 12 14 22 4"></polyline>
                            </svg>
                        </button>
                        <button class="btn btn-icon btn-secondary" title="Export Selected">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                        </button>
                        <button class="btn btn-icon btn-secondary" title="Export All">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14 2 14 8 20 8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10 9 9 9 8 9"></polyline>
                            </svg>
                        </button>
                        <button class="btn btn-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5Z"></path>
                                <path d="M12 5L8 21l4-7 4 7-4-16"></path>
                            </svg>
                            Cart <span style="background: white; color: #2563eb; padding: 2px 8px; border-radius: 12px; margin-left: 4px;">2</span>
                        </button>
                    </div>
                </div>

                <div class="results-table">
                    <!-- Empty State -->
                    <div class="empty-state">
                        <div class="empty-icon">🔍</div>
                        <h4 class="empty-title">No results yet</h4>
                        <p class="empty-text">Start searching to find Facebook interests for your campaigns</p>
                    </div>

                    <!-- Results Table (hidden by default) -->
                    <table style="display: none;">
                        <thead>
                            <tr>
                                <th style="width: 50px;">Select</th>
                                <th style="width: 50px;">Favorite</th>
                                <th>Name</th>
                                <th>ID</th>
                                <th>Audience Size</th>
                                <th>Type</th>
                                <th>Path</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="resultsBody">
                            <!-- Results will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Search History -->
            <div class="history-section">
                <div class="history-header">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <h3 class="history-title">Search History</h3>
                </div>
                <div class="history-list">
                    <div class="history-item">
                        <span class="history-term">cinema</span>
                        <span class="history-time">2 minutes ago</span>
                    </div>
                    <div class="history-item">
                        <span class="history-term">projector</span>
                        <span class="history-time">5 minutes ago</span>
                    </div>
                    <div class="history-item">
                        <span class="history-term">projector</span>
                        <span class="history-time">8 minutes ago</span>
                    </div>
                    <div class="history-item">
                        <span class="history-term">'projector', 'cinema'</span>
                        <span class="history-time">12 minutes ago</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Tab switching
        const singleTab = document.getElementById('singleTab');
        const bulkTab = document.getElementById('bulkTab');

        singleTab.addEventListener('click', () => {
            singleTab.classList.add('active');
            bulkTab.classList.remove('active');
        });

        bulkTab.addEventListener('click', () => {
            bulkTab.classList.add('active');
            singleTab.classList.remove('active');
        });

        // Form submission simulation
        document.querySelector('.search-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Show loading state
            const resultsSection = document.querySelector('.results-section');
            const emptyState = resultsSection.querySelector('.empty-state');
            const table = resultsSection.querySelector('table');
            
            emptyState.style.display = 'none';
            table.style.display = 'none';
            
            resultsSection.insertAdjacentHTML('beforeend', '<div class="loading"><div class="spinner"></div></div>');
            
            // Simulate API call
            setTimeout(() => {
                const loading = resultsSection.querySelector('.loading');
                if (loading) loading.remove();
                
                // Show sample results
                table.style.display = 'table';
                document.querySelector('.results-badge').textContent = '3';
                
                const tbody = document.getElementById('resultsBody');
                tbody.innerHTML = `
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td><button class="favorite-btn">⭐</button></td>
                        <td class="interest-name">Cinema</td>
                        <td class="interest-id">6003139266461</td>
                        <td class="audience-size">850,234,000</td>
                        <td><span class="interest-type">Interest</span></td>
                        <td class="interest-path">Entertainment > Movies</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn">Add to Pool</button>
                                <button class="action-btn">Details</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td><button class="favorite-btn">⭐</button></td>
                        <td class="interest-name">Movie Theater</td>
                        <td class="interest-id">6003248922132</td>
                        <td class="audience-size">425,123,000</td>
                        <td><span class="interest-type">Interest</span></td>
                        <td class="interest-path">Entertainment > Movies > Venues</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn">Add to Pool</button>
                                <button class="action-btn">Details</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td><button class="favorite-btn active">⭐</button></td>
                        <td class="interest-name">Film Festival</td>
                        <td class="interest-id">6003020834693</td>
                        <td class="audience-size">125,456,000</td>
                        <td><span class="interest-type">Interest</span></td>
                        <td class="interest-path">Entertainment > Events</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn">Add to Pool</button>
                                <button class="action-btn">Details</button>
                            </div>
                        </td>
                    </tr>
                `;
            }, 1500);
        });

        // Favorite button toggle
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('favorite-btn')) {
                e.target.classList.toggle('active');
            }
        });

        // History item click
        document.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', () => {
                const searchTerm = item.querySelector('.history-term').textContent;
                document.querySelector('.search-input').value = searchTerm;
            });
        });
    </script>
</body>
</html>