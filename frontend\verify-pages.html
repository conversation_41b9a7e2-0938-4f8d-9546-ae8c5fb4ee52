<!DOCTYPE html>
<html>
<head>
    <title>TargetWise Page Verification</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .box { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
        .reference { background-color: #f0f8ff; }
        .current { background-color: #f0fff0; }
        h3 { margin-top: 0; }
        .status { padding: 5px 10px; border-radius: 4px; display: inline-block; margin: 5px 0; }
        .success { background: #4caf50; color: white; }
        .error { background: #f44336; color: white; }
        .warning { background: #ff9800; color: white; }
        .code { background: #f4f4f4; padding: 10px; border-radius: 4px; overflow-x: auto; }
        iframe { width: 100%; height: 600px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>TargetWise Page Verification</h1>
    <p>Comparing current implementation with reference templates in docs/original-enhanced-files/</p>

    <div id="results"></div>

    <script>
        const results = document.getElementById('results');

        // Pages to verify
        const pages = [
            {
                name: 'TargetWise Main',
                current: '/',
                reference: '/docs/original-enhanced-files/targetwise-main-enhanced.html',
                module: '/src/pages/TargetWiseMain.js'
            },
            {
                name: 'Dashboard',
                current: '/dashboard',
                reference: '/docs/original-enhanced-files/enhanced-targetwise-dashboard.html',
                module: '/src/pages/Dashboard.js'
            },
            {
                name: 'Interest Pool',
                current: '/pool',
                reference: '/docs/original-enhanced-files/interest-pool-enhanced.html',
                module: '/src/pages/InterestPool.js'
            },
            {
                name: 'Interest Search',
                current: '/search',
                reference: '/docs/original-enhanced-files/interest-search-enhanced.html',
                module: '/src/pages/InterestSearchEnhanced.js'
            },
            {
                name: 'Interest Suggestions',
                current: '/suggestions',
                reference: '/docs/original-enhanced-files/interest-suggestions-enhanced.html',
                module: '/src/pages/InterestSuggestions.js'
            }
        ];

        async function verifyPage(page) {
            const section = document.createElement('div');
            section.innerHTML = `<h2>${page.name} Page</h2>`;

            // Check if module exists
            try {
                const moduleResponse = await fetch(page.module);
                if (moduleResponse.ok) {
                    section.innerHTML += '<span class="status success">✓ Module exists</span><br>';
                } else {
                    section.innerHTML += '<span class="status error">✗ Module not found</span><br>';
                }
            } catch (error) {
                section.innerHTML += '<span class="status error">✗ Module error: ' + error.message + '</span><br>';
            }

            // Create comparison view
            const comparison = document.createElement('div');
            comparison.className = 'comparison';
            
            // Reference template
            const refBox = document.createElement('div');
            refBox.className = 'box reference';
            refBox.innerHTML = `
                <h3>Reference Template</h3>
                <p>Path: ${page.reference}</p>
                <iframe src="${page.reference}" title="Reference"></iframe>
            `;
            comparison.appendChild(refBox);

            // Current implementation
            const currentBox = document.createElement('div');
            currentBox.className = 'box current';
            currentBox.innerHTML = `
                <h3>Current Implementation</h3>
                <p>Route: ${page.current}</p>
                <iframe src="${page.current}" title="Current"></iframe>
            `;
            comparison.appendChild(currentBox);

            section.appendChild(comparison);

            // Add checklist
            const checklist = document.createElement('div');
            checklist.innerHTML = `
                <h3>Verification Checklist for ${page.name}:</h3>
                <ul>
                    <li>Layout structure matches reference</li>
                    <li>All UI components are present</li>
                    <li>Colors and styling are consistent</li>
                    <li>Interactive elements are functional</li>
                    <li>Responsive design works correctly</li>
                </ul>
            `;
            section.appendChild(checklist);

            results.appendChild(section);
            results.appendChild(document.createElement('hr'));
        }

        // Verify all pages
        pages.forEach(page => verifyPage(page));

        // Add performance summary
        const summary = document.createElement('div');
        summary.innerHTML = `
            <h2>Performance Analysis Summary</h2>
            <div class="code">
                <h4>Identified Issues:</h4>
                <ol>
                    <li><strong>API Backend Not Running:</strong> The main performance issue was that the API backend was not running, causing API calls to fail and the frontend to hang waiting for responses.</li>
                    <li><strong>Service Worker:</strong> The service worker is minimal and just passes through requests - not causing issues.</li>
                    <li><strong>CSS Loading:</strong> All CSS files are properly loaded and linked.</li>
                    <li><strong>Module Loading:</strong> ES6 modules are loading correctly with proper imports.</li>
                </ol>
                
                <h4>Solutions Implemented:</h4>
                <ol>
                    <li>Created a mock API server (mock-api-server.py) to handle API requests during development</li>
                    <li>Updated server.py to proxy API requests to the backend server</li>
                    <li>Both servers are now running (frontend on :8080, API on :8000)</li>
                </ol>
                
                <h4>Next Steps:</h4>
                <ol>
                    <li>Install proper Python dependencies (uvicorn, fastapi) to run the real API backend</li>
                    <li>Verify all page layouts match the reference templates</li>
                    <li>Test all interactive features</li>
                    <li>Optimize loading performance with lazy loading if needed</li>
                </ol>
            </div>
        `;
        results.appendChild(summary);
    </script>
</body>
</html>