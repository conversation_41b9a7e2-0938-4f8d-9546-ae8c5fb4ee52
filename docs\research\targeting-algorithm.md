# Smart Algorithm for Building a 12-Column “Algorithmic Targeting 2.0” Sheet

**Overview:** *Algorithmic Targeting 2.0* is a structured Facebook ad targeting method that uses 12 distinct interest columns and 3 targeting styles to systematically test audience segments. Each column represents a category of interests related to the product (e.g. niche keywords, magazines, brands, etc.), and interests are grouped by theme to maintain relevance. The goal is to cover a wide range of interest “angles” on the niche so that we quickly gather data on what targeting works best for the product. Using Meta’s Marketing API, we can design an algorithm that dynamically builds such a 12-column targeting sheet (for example, for the *Magcubic HY300 4K Projector* case) in real time. Below, we present: (1) functional requirements for this smart targeting algorithm, (2) a capability matrix of the relevant Meta API endpoints, (3) audience reach estimation logic, (4) guidance on integrating the solution into a web application (from Postman queries to backend implementation), and (5) approaches for interest classification and taxonomy mapping using NLP and rules.

## 1. Functional Requirements for the Targeting Algorithm

* **Input and Data Gathering:** The tool accepts input about the product niche – for example, a CSV of seed interests or keywords relevant to *Magcubic HY300 4K Projector*. It will then query Meta’s Marketing API to fetch fresh data on those interests (especially updated audience size in the target market, e.g. U.S.). It should also be able to discover additional related interests beyond the input list by leveraging Facebook’s interest suggestion features (the “Suggestions” API). This ensures the algorithm uses only real-time data and relevant interests (no static or outdated interest lists).

* **Interest Discovery via API:** For each seed keyword or interest, the algorithm uses **real-time interest search** endpoints to find matching or similar targeting interests from Facebook. Specifically, it will use:

  * *Targeting Search* (`/targetingsearch`) to look up interests by name or keyword. For example, searching for “home theater” or “projector” yields interest IDs/names and size estimates for matching interests in Facebook’s database.
  * *Targeting Suggestions* (`/targetingsuggestions`) to retrieve **related interest suggestions** given one or more seed interests. For example, inputting an interest like “Home theater” into suggestions might return related interests such as “Surround sound,” “4K resolution,” or “Netflix,” which Facebook’s algorithm deems similar.
  * *Targeting Browse* (`/targetingbrowse`) if needed, to navigate the taxonomy of interests (e.g. list out sub-categories of interests or discover interests under a broader category).

* **Interest Classification (12 Columns):** All collected interests (from the CSV and any newly fetched suggestions) are **auto-classified into 12 distinct columns** corresponding to the Algorithmic Targeting 2.0 categories. The twelve standardized columns for this methodology are: **1)** Keyword Mix < 1M, **2)** Keyword Mix > 1M, **3)** Magazine Flex, **4)** Websites & Keywords, **5)** Niche Categories, **6)** Passionate Flex, **7)** TV Shows & Groups (influencers/pop culture), **8)** Core “Neck” Flex, **9)** Related Niche, **10)** Buyers Flex, **11)** Affinity Flex, **12)** Store “Light” Flex. (Each is defined in the *Algorithmic 2.0* framework – e.g. Column 1 is a mix of small niche interests under 1M audience, Column 3 is interests in relevant magazines or media, Column 5 is niche product brands, etc.) The algorithm must assign each interest to the appropriate column based on its theme. By keeping each column thematically “pure” (only closely related interests), we preserve relevance.

* **No Duplicate Interests:** The same interest should **not appear in more than one column**, ensuring each column tests a unique set of users. If an interest could fit multiple categories, the algorithm will choose the best-fitting column and exclude it from others. Especially, if an interest is chosen as a **narrowing interest** in one of the columns (see targeting style 2 below), it must **not be used anywhere else at all**. This strict deduplication prevents overlapping audiences across the 12 ad sets.

* **33% Targeting Style Mix (4-4-4 Rule):** The 12 columns are split evenly into three targeting *styles*, with **exactly 4 columns of each style**:

  * *Style 1: OR-Only Targeting* – 2–8 interests combined with OR logic, with **no narrow further layering**. (These typically group several smaller related interests together in one ad set.)
  * *Style 2: Narrowed Targeting* – 2–8 interests in the first layer **narrowed by 1 broad interest** in the second layer. (This “flex” targeting ensures the broad interest acts as a filter for the others; e.g. a set of broad tech keywords narrowed by “Home cinema” to keep the audience specific to home-theater context.)
  * *Style 3: Single-Interest Targeting* – a **single interest per ad set** (no combination, one interest only).

  The algorithm must maintain approximately one-third of the columns in each style (4 columns each). This is known as the “33% rule,” ensuring a balanced test between broad (multi-interest) audiences, refined narrow-layer audiences, and very focused single-interest audiences. The tool should either enforce a predetermined mapping of which column number uses which style (as in the example sheet), or dynamically assign styles to columns such that the 4-4-4 split is met. For instance, in the example projector sheet, Columns 1, 3, 6, 10 were style 1 (OR-only), Columns 2, 4, 5, 11 were style 2 (OR+Narrow), and Columns 7, 8, 9, 12 were style 3 (single interests). The algorithm can use a similar pattern or logic (e.g. assign style 1 to categories that have many small interests, style 2 when a narrowing filter is beneficial, style 3 for broad one-interest tests).

* **Audience Size Constraints (\~4–5M “Sweet Spot”):** Each column’s audience size (the potential reach of that targeting group) should be **approximately 4 to 5 million** people in the chosen market. Facebook’s ad algorithm tends to perform well with audiences in this range – large enough for algorithmic optimization but not so broad that it’s inefficient. The algorithm should aim to build each column’s interest combination such that the estimated reach is in the \~4–5M range (for example, in the US). In practice, some columns may naturally fall a bit lower (2–3M) if the niche is very narrow, which can be acceptable. However, if a column’s estimated reach is significantly below the target and cannot be increased (or if it greatly exceeds 5M), the tool should flag it for review. Specifically, the requirement is to **validate that every column’s reach lies between roughly 4–5 million**, and if not, apply adjustments or mark that column as needing manual intervention.

  * To **increase** a column’s reach toward \~5M: the algorithm can add more related interests (in OR) if available, or use slightly broader interests in that category. If it’s a style-2 column, perhaps use a somewhat broader “narrow” interest or remove the narrow if overly restrictive.
  * To **decrease** a column’s reach (if it overshoots significantly above 5M): consider adding a narrow filter (converting it to style 2) or breaking the interests into two groups. Alternatively, if one interest is extremely large (tens of millions), it might be better used as a single-interest column (style 3) rather than grouped with others, to let Facebook’s algorithm optimize within that broad interest.
  * The tool will use the Marketing API’s reach estimate capabilities to check these sizes (discussed later). If despite all adjustments a column cannot be brought into range – for example, a very niche category might max out at \~1M even after adding all related interests – the algorithm should flag that column with a “**NEEDS MANUAL REVIEW**” note for the marketer. This alert tells the user that human judgment is needed, perhaps to accept a smaller audience or reconsider that targeting approach.

* **Output Format – Targeting Sheet Generation:** The final output is a clean 12-column sheet (e.g. an Excel or CSV file) ready to paste into Facebook Ads Manager or use in bulk creation tools. Each column contains the list of interests for that audience (with appropriate delimiters for OR logic, and an indication of any narrow interest if style 2). For example, the sheet may have each column titled with the column name (like “Magazine Flex”, “Buyers Flex”, etc.), and the interests listed in a single cell separated by commas or semicolons (as Facebook accepts a comma-separated list for OR targeting), with a special notation or separate line for the “Narrow” interest if applicable. The algorithm should export this in a structured way so that it’s **ready to be copied into Ads Manager** with minimal manual editing. In addition, as a bonus, the system may produce a quick **summary PDF** that documents what interests were used in each column and their rationale (for record-keeping or client communication).

* **Robustness and Logging:** The system should log each API call and its result (for transparency and debugging). If any interest queries fail or return errors (e.g. due to rate limits or network issues), the algorithm should handle these gracefully – possibly by retrying after a short delay, or skipping that interest with a warning. Given that the tool may make many API calls (searches, suggestions, reach estimates), it must also respect Meta’s API rate limits (covered below in the API section). The code should be well-structured and documented, as it may be extended to other products and maintained over time.

In summary, **success** for the algorithm means producing an **error-free 12-column targeting sheet that adheres to all AT2.0 rules** – no duplicate interests across columns, a 4-4-4 balance of targeting styles, each column \~4–5M reach – and automatically flagging any columns that cannot meet the criteria. The entire process (from uploading input to downloading the sheet) should be seamless in a simple UI, so that marketers **never have to manually hunt for interests or tinker in Ads Manager** to assemble these audiences.

## 2. Meta Marketing API Endpoints & Capabilities

To build this tool, we leverage Meta’s Marketing API, particularly the **Detailed Targeting** endpoints that allow programmatic access to interest targeting options. Below is a summary of each relevant endpoint, including its purpose, key parameters, output fields, and any usage notes or limits:

### **Ad Account Targeting Search (`GET /act_{ad_account_id}/targetingsearch`)**

* **Purpose:** The *targetingsearch* endpoint provides a unified search for targeting options. We use it to find interest IDs and metadata by querying a keyword. It’s essentially the programmatic equivalent of typing a term into the Detailed Targeting box in Ads Manager and seeing suggestions appear. For example, searching for `"projector"` or `"home theater"` via this API will return a list of matching interests (as well as possibly behaviors or other targeting types, if not filtered).

* **Key Parameters:**
  • `q` – The search query string (e.g. `"4K projector"`). Partial terms are allowed; the API will return any targeting entities that match this string.
  • `type` – The type of targeting entities to search for. Common values are `"adinterest"` (for interests) or other types like `"adgeolocation"`, `"adeducationmajor"`, etc. For interest targeting, we specify `type=adinterest` to restrict results to interest-based targeting options. (In newer versions of the API, this might also be simply `type=interests` – the naming varies in different SDKs, but the concept is to filter to interest targeting.)
  • `limit` – (Optional) The maximum number of results to return. By default the API might return a certain number (e.g. 25); setting a higher limit (say 100 or 1000) can retrieve more matching interests in one call if many exist.
  • `locale` – (Optional) Locale for result names/descriptions. If we want the interest names in English, we ensure locale is en\_US (typically the default if the ad account’s locale is US).

* **Output:** Returns a JSON list of matching targeting entities. Each result typically includes:
  • `id` – The unique identifier of the interest (to be used in ads targeting).
  • `name` – The name of the interest (human-readable).
  • `type` – The category of targeting (for interests, it will indicate “interest” or similar).
  • `path` – An array of strings indicating the taxonomy path for this interest. For example, an interest *Advertising* might have `"path": ["Interests","Business and industry","Advertising"]`, meaning it’s an interest under the Business and Industry category. This can be useful for understanding the general topic of the interest.
  • `audience_size_lower_bound` and `audience_size_upper_bound` – **Estimated audience size range** for the interest. As of late 2021, Facebook no longer provides an exact audience size in the targeting search results; instead, it provides a lower-bound and upper-bound estimate of the potential reach. (Prior to this change, an `audience_size` field was provided as an exact (but often rounded) number. That field was deprecated on Oct 27, 2021, replaced by the range fields to align with privacy and consistency improvements.) These bounds give us a sense of scale (e.g. interest X might have 1M–1.5M people).
  • `description` – A short description of the interest, if available. Often this is empty for many interests, but sometimes it might contain a phrase explaining the interest.
  • `topic` – A broad categorization of the interest (often this corresponds to a top-level category from the path). For example, *Advertising* had `"topic": "Business and industry"` in the result. This is essentially the high-level domain under which Facebook groups that interest.

* **Usage & Best Practices:** We will use `/targetingsearch` for **initial discovery of interests** by keyword. This is useful when we have a product-related keyword and want all related interests. For instance, to build the projector sheet, we might search terms like “projector”, “home cinema”, “4K”, “AV receiver”, “Netflix”, etc., to gather an initial pool of interests. We must be mindful that the search can return multiple entries for similar names – for example, searching “Adele” returns several results (the singer, album names, etc.), each with a `disambiguation_category` to differentiate them. Our algorithm should pick the one that matches our intent (usually the one with largest audience and relevant category). The *targetingsearch* results are global or not locale-specific by default (the audience size is typically global or US depending on account settings; we may later refine by country with reach estimates). We should also handle pagination if the result set is large (the API will provide a `next` cursor if more results are available beyond the `limit`). Rate-limit wise, each search call counts against the account’s Marketing API rate limit score (which allows a certain number of calls per 5-minute window, typically a maximum score of 60 with a decay over 300 seconds). To avoid hitting limits, we can space out calls or batch multiple queries in one API call (Facebook supports batch requests of up to 50 calls in one HTTP request).

### **Ad Account Targeting Suggestions (`GET /act_{ad_account_id}/targetingsuggestions`)**

* **Purpose:** The *targetingsuggestions* endpoint provides recommendations for related targeting options based on a given list of seed interests (or other targeting specs). This mirrors the **“Suggestions” feature in Facebook Ads Manager** where, after selecting some interests, Facebook proposes additional similar interests. We use this to expand our interest list and discover interests we might not have thought of, but which Facebook’s graph indicates are relevant to our seeds.

* **Key Parameters:**
  • `targeting_list` – **Required.** An array of targeting spec objects that we provide as the seeds for suggestions. For example, `targeting_list=[{'type':'interest','id': <ID1>}, {'type':'interest','id': <ID2>} ]`. Each object must include a `type` (such as “interests”) and the `id` of a known interest (or other targeting type) to base suggestions on. We typically will pass just one seed interest at a time to get suggestions specifically related to that interest. However, you can also pass multiple seeds; Facebook may then return suggestions that are related to the **combination** or any of them (the exact behavior isn’t fully documented, but it often broadens the pool).
  • `limit` – (Optional) Max number of suggestions to return. If not specified, a default (perhaps 25) is returned. We might increase this if we want a larger list of suggestions for a given seed.
  • `limit_type` – (Optional) We can sometimes specify what type of suggestions to retrieve. By default, the API might return any mix of interests, behaviors, etc. Setting `limit_type=interests` can restrict suggestions to only interest-type results, which is usually what we want (since our targeting sheet focuses on interests rather than behaviors or demographics).
  *(Note:* The documentation for `targetingsuggestions` isn’t publicly detailed, but from usage it appears these parameters apply. The Facebook PHP SDK shows `targeting_list` and optionally `whitelisted_types` which serve a similar purpose.)

* **Output:** Returns a JSON list of suggested targeting options (in a similar format to targeting search results). Each suggestion entry will include:
  • `id`, `name`, `type` – identifying the interest or other targeting entity suggested. These will most often be interests (type “interest” or “interests”).
  • `audience_size_lower_bound`, `audience_size_upper_bound` – estimated size range of that interest’s audience (similar to targeting search output).
  • `path`, `topic`, `description` – possibly provided as well, like in search results, giving category context and any description of the interest.

  For example, if our seed in `targeting_list` is the interest “Home theater”, the API might return suggestions such as “Surround sound”, “Hi-Fi audio”, “Netflix”, “Blu-ray Disc” etc., each with their own ID and size. These are interests that people interested in “Home theater” are also commonly interested in. It essentially leverages Facebook’s data on user affinity to suggest closely related interests.

* **Usage & Best Practices:** We will use `/targetingsuggestions` to systematically **expand each category of interests**. A strategy is to take at least one representative interest from each of our 12 categories and get suggestions from it. For example:

  * For the *Magazine* category, use a known tech magazine interest (like “Wired”) as a seed to get other magazine suggestions (e.g. it might suggest “PC Magazine” or “TechCrunch”).
  * For *Niche Categories* (brands of projectors), use one brand (say “Epson”) to get suggestions (could return other electronics brands).
  * For *Affinity* category, use a known broad interest (like “Home cinema”) to get pages or interests that have high affinity with it (maybe suggests related hobbies).
  * And so on for each.
    We should filter the suggestions returned to ensure they fit the intended category – not all suggestions will be relevant. The interest classifier (discussed later) can help decide if a suggestion belongs in the same category as the seed or should be allocated elsewhere. The suggestions API often returns very relevant interests, but sometimes they may be too broad or tangential, so a human-defined threshold or check is useful.

  In terms of **rate limiting and efficiency**, one approach is to batch multiple seeds in one call by including several objects in `targeting_list`. However, note that the API might then return a combined set of suggestions (not necessarily grouped by which seed triggered them). For clarity, we might simply call it separately for each seed interest sequentially. We must ensure we don’t flood the API with too many calls at once; Facebook’s Marketing API allows a high number of calls per hour (up to 700k calls/hour per account in some cases) but each call still counts against the 5-minute window score. Using caching (storing suggestions we’ve seen recently) can reduce duplicate calls if the same interest is used again. Also, the suggestions endpoint might have an internal limit on how many unique suggestions it can return per seed (if we need more, a different seed or slightly altered approach might yield new ones).

### **Ad Account Targeting Browse (`GET /act_{ad_account_id}/targetingbrowse`)**

* **Purpose:** The *targetingbrowse* endpoint allows traversal of Facebook’s detailed targeting hierarchy. It provides a “browseable” taxonomy of interests and other targeting types as a tree. We can use this to find **interest categories and their children**. For example, we might retrieve all sub-categories under “Interests > Electronics” or find which parent category a given interest belongs to. This is somewhat akin to the older Audience Insights interest categories.

* **Key Parameters:**
  • `limit_type` – (Optional) Filter the type of results to retrieve. For instance, `limit_type=interests` restricts the output to interest categories/nodes (excluding behaviors or demographics). This is useful to just navigate interest taxonomy.
  • `id` or `parent_id` – (Though not clearly documented, typically you can provide a parent category ID to get its children.) If no specific parent is provided, the API might return top-level categories. The documentation says it returns a “flat list” with a parent key to reconstruct the tree. This implies the results include category nodes with a field indicating the parent category.
  • `locale` – (Optional) Locale for names and descriptions.

* **Output:** A list of targeting nodes (which could be broad interest categories or actual interests). Fields include:
  • `id` – If it’s a specific interest, an ID similar to those from search. If it’s a category (like “Technology > Photography”), it might also have an ID (sometimes category IDs are not usable as targeting directly, but just identifiers for the taxonomy).
  • `name` – Name of the interest or category (e.g. “Photography” or “Harvard University” in example).
  • `type` – The type of node: likely “interests” for interest and maybe “adTargetingCategory” for pure category nodes. In some SDKs, everything is returned as a unified type.
  • `path` – The full path of categories to that node. E.g. an interest might show its lineage. In an example, “Harvard University” had `"path": ["Interests","Additional Interests","Harvard University"]`. Here “Additional Interests” appears to be a subcategory under “Interests” grouping miscellaneous interests.
  • `parent` (possibly) – A parent category ID if it’s a child node (not explicitly shown in the snippet, but “parent” might be an internal field to reconstruct the tree).
  • `audience_size` or size bounds – It might return audience sizes as well. (The example shows `audience_size: 22,821,890` for Harvard University, which suggests either the example was from before the audience\_size deprecation, or the browse endpoint might still output it or a cached value. Likely in newer versions, it would also use lower\_bound/upper\_bound fields.)
  • `description` – For category nodes, this can be a human description of that category. Browse is noted as the only way to get the **description** of an interest/category (often the description field in search results is blank; browse might provide it if available).

* **Usage & Best Practices:** In our algorithm, *targetingbrowse* is a supplementary tool. We might not need to enumerate the entire interest tree, but it can be useful for **identifying categories of interests to target**. For instance, if we know our product is in “Home Theater” niche, we might browse categories under *Interests > Home and Entertainment* to see if Facebook has a predefined category for “Home cinema” or related concepts, and gather interests from there. Another use is to verify if a given interest belongs to a certain category (by checking its path). We should note, however, that many useful interests are under “Additional Interests” which is a generic bucket, so browse might not always neatly categorize everything we need.

  Because *targetingbrowse* can output a large list, use caution with large requests. If we retrieve all interests under a broad category, it could be hundreds of entries. We might use a targeted approach: e.g. find the category node for “Projectors” if it exists and list its children (like brand names). In practice, one might call `targetingsearch` with a broad term first, see the `path` of an interest, and then use `targetingbrowse` if we want all siblings in that path category.

  As for rate limiting, browsing large lists is a heavier operation. We should only do it if necessary and possibly cache the results. For example, if the taxonomy of projector-related interests is fetched once, store it so subsequent runs don’t fetch it again unless needed.

### **Reach Estimate and Validation Endpoints**

In addition to the above, there are endpoints for estimating reach and validating targeting combinations:

* **Reach Estimate (`GET /act_{ad_account_id}/reachestimate`)** – This endpoint returns the estimated audience reach for a given targeting spec. We supply a full `targeting_spec` (which can include geo location, age, gender, interests, behaviors, etc.) and the API returns a **range** of reachable users (minimum and maximum) for that spec. This is useful for *precisely checking the combined reach* of a set of interests (especially for OR combinations or narrow combos, where simply summing individual interests is inaccurate). The output includes fields like `users_lower_bound` and `users_upper_bound` (or similarly named fields) which correspond to the potential reach range. Using this endpoint requires an access token with ads management permissions and possibly that the app/account is enabled for ads API. We will use this endpoint after forming each column’s interest set to verify its reach. (Note: In newer API versions, reachestimate has been replaced or merged into a unified **Reach and Frequency Prediction** service, which provides reach curves. However, the principle remains – we can get an audience size estimation via API. If the direct endpoint is not available, an alternative is `GET /act_{account}/delivery_estimate` as part of creating an ad set targeting spec.)

* **Targeting Validation (`GET /act_{ad_account_id}/targetingvalidation`)** – This endpoint can check if given target IDs or specs are valid and deliverable. We can provide a list of interest IDs (and other criteria) and it returns whether each is valid (still targetable) and may also echo back details like name and audience size. This could be used to ensure that interests we pulled via search are actually valid in the context (sometimes certain interests might be deprecated or not available in certain locations or under special ad categories). In practice, if we directly search and use IDs, they should be valid; but this could be a safeguard or for debugging (especially if an interest ID from a global search isn’t valid for a specific country, although usually interests are global, with reach varying by country). The validation response also returns an audience size (possibly global) for each ID, but again likely now as ranges and mainly to indicate validity.

**API Rate Limits and Constraints:** All these endpoints fall under Marketing API usage, which is governed by a throttling mechanism. Each ad account has a “score” that accumulates with each call: the maximum score is 60 and decays over time (5 minutes). If you hit 60, further calls are blocked for a 5-minute cooldown. Different API calls may consume different amounts of score (complex calls like reach estimate might cost more than simple reads). Additionally, there are broader app-level rate limits (for example, in one real-case documentation, Facebook mentions up to 700k Marketing API calls per hour per ad account for approved apps, although this can vary). Our algorithm needs to be mindful of these limits by:

* Batching calls when possible (the Graph API supports batched requests to combine multiple queries into one HTTP call).
* Spacing out calls and using delays or waiting if we approach limit (monitor response headers which include `X-Business-Use-Case-Usage` or similar that tells current consumption). We can programmatically sleep/retry if we get a rate limit error (HTTP 17 error code).
* Caching results (e.g. if we search “Netflix” once and get the interest ID and size, store it so we don’t search it again in the same session or soon after).

Finally, ensure you have proper **API access permissions**. To use these endpoints, our app or token must have the **ads\_management** (or **ads\_read** for read-only) permission on the ad account. If the tool is only for an in-house account, a System User token with Standard access and the ad account authorized should suffice. If it will manage other clients’ accounts, the app will need to go through Meta’s App Review for Ads API (to get Advanced Access). In development, using your own account, you can get a long-lived token for ads\_management for your ad account and use it in Postman to test these endpoints.

## 3  Audience Reach-Estimation Model Logic — 100 % API-Validated

**Goal:** Every Algorithmic 2.0 column must finish with a country-specific reach of 4–5 million users. We achieve that by querying Meta’s own reach-prediction engine for each completed `targeting_spec`. All arithmetic shortcuts have been removed.


### 3.1  Why API-Validated Sizing?

True overlap accounting – Facebook alone knows the exact user-level intersection/union of interests. Summing individual sizes routinely over-estimates by 30–70 %.

Policy alignment – iOS 14+, GDPR, and Meta’s privacy budget mean the public search sizes are deliberately fuzzed. The delivery-estimate endpoint already includes these constraints, so what we see is what the ad-auction will deliver.

Consistency across launches – When the same sheet is rebuilt a week later, the API reflects any size drift; no stale heuristics.

---

### 3.2  Building Each Targeting Spec

| Style            | JSON Layout                                                                 | Example Snippet                                                |
|------------------|-----------------------------------------------------------------------------|----------------------------------------------------------------|
| **1  OR-only**   | `{ "interest_any": [ …IDs… ] }`                                             | Home-theater magazines OR’d together                           |
| **2  OR + Narrow** | `{ "interest_any": [ …IDs… ], "flexible_spec": [ { "interests": [ ID_narrow ] } ] }` | Smart-TV, Blu-ray, HDTV **AND** must also match Home Cinema |
| **3  Single**    | `{ "interest_any": [ ID_single ] }`                                         | ID of broad core term “Home cinema”                            |

Other fixed keys applied uniformly:

```json
"geo_locations": { "countries": [ "US" ] },
"age_min": 18,
"age_max": 65
````

---

### 3.3  Validation Loop (per column)

1. **Draft** the spec from classifier output.
2. **Batch-call**
   `POST /act_{account}/delivery_estimate?optimization_goal=REACH`
   for up to 50 specs at once. One batch covers all 12 columns.
3. **Interpret API range** ▶
   `midpoint = (lower + upper) / 2`

   * If **upper > 5 M** → add Narrow, drop broadest ID, or split into Style 3.
   * If **lower < 4 M** → add extra OR interests or remove Narrow.
   * After two passes, if midpoint still outside 4–5 M → flag **NEEDS MANUAL REVIEW**.
4. **Decision**

   * **Pass** if 4 M ≤ midpoint ≤ 5 M.
   * **Expand** (< 4 M): add extra long-tail interests or remove the Narrow filter.
   * **Constrain** (> 5 M): add Narrow, drop the broadest ID, or spin the largest ID off into a new Style 3 test.
5. **Cache** `SHA1(targeting_spec)` → `upper_bound` for 24 h (Redis).
6. After two adjustment attempts, unsolved specs are flagged **NEEDS MANUAL REVIEW**.

#### Code Skeleton

```python
payloads = [{"targeting_spec": spec} for spec in specs]
res = fb.batch(
    endpoint="/delivery_estimate",
    method="POST",
    body=payloads
)
for est, spec in zip(res, specs):
    mid = (est["users_lower_bound"] + est["users_upper_bound"]) / 2
    if 4_000_000 <= mid <= 5_000_000:
        accept(spec)
    else:
        tweak(spec)
```

---

### 3.4  Edge-Case Handling

* **Low-volume niches (< 1 M even after max OR):**
  Accept if the column’s angle is strategically valuable (e.g., Store Light Flex); otherwise merge with a related column.

* **Super-broad Globals (> 20 M US):**
  Only allowed in a Style 3 single-interest ad set. Rely on Facebook’s internal optimisation.

* **Token scope loss:**
  If the API returns `190 | OAuthException`, immediately surface an error banner and pause further processing.

**Handling Range Outputs:** Since the API gives a range `[lower_bound, upper_bound]`, the algorithm reads midpoint/upper-bound from the API response as the estimated audience, or simply uses the upper bound if we want to ensure not to exceed 5 M. For instance, if an interest returns 6 M–7 M as range, it’s likely a bit above our desired range. We could still allow it if it’s close, or decide to narrow further. Notably, after iOS14+ changes, Facebook’s reach estimates tend to be broader ranges and less precise. We’ll treat the upper bound as the safe estimate of size (worst-case reach). If that upper bound > 5 M, consider trimming; if lower bound < 4 M but upper bound > 4 M, it’s probably okay (means likely around 4 M). If even the upper bound is well below 4 M (say 1 M–2 M), then we know this column is under the threshold – we might try adding more interests if possible. If not, that’s when “Needs Manual Review” flag is appropriate, as the algorithm can’t magically create more audience if the niche is limited.

**Overlap and Deduplication Effects:** One important aspect is that because we are not allowing the same interest in multiple columns, the audiences are inherently intended to be distinct. However, some overlap between columns is inevitable (e.g. people interested in “Netflix” (Affinity Flex) might also be in “Home Cinema” (Core Flex) – but since we placed each interest only in one, we rely on Facebook’s algorithm to find those overlaps naturally when running ads). Our reach estimates per column are done in isolation of each other. We do not subtract overlaps between columns in these estimates, because each column is a separate ad set (and Facebook’s reach estimate doesn’t consider other ad sets). In practice, when running simultaneously, there could be audience overlap across ad sets, but that’s not directly our concern in building the sheet (except to be aware of potential competition between ad sets).

**Final Check and Adjustment Loop:** The algorithm can incorporate a loop: after assembling all columns and computing estimated reach for each, check which columns are out of the 4–5 M range and attempt adjustments:

* **If a column is too small:**
  Perhaps we missed some interests. The tool could call the suggestions API again with one of the interests in that column to find more ideas to add. Or relax criteria: e.g., if it’s a narrowed set, consider removing the narrow (turn it into style 1 OR). For example, if Store Light Flex (specialty retailers) only gave us one small store interest with 800 k reach, we might accept it as is (since by design it’s a narrow niche column). The documentation does note it’s okay if some end up \~800 k as long as it’s a focused high-quality audience. We flag it but might still use it.

* **If a column is too large:**
  Decide if we can split it into two columns. However, since we fixed 12 columns, splitting one means dropping another – not ideal. Instead, if one column (say Core broad interest “Home Cinema”) is, e.g., 20 M reach in US, we included it as single interest counting on Facebook to optimize. We trust the algorithm for broad interest columns; we just note it might overshoot 5 M but given it’s single-interest style, it’s an intentional broad test. Another approach for broad interests is to narrow them by a behavior like Engaged Shoppers or by an age group to bring size down, but that changes the nature of the test. Generally, if something is extremely broad and not essential, we might not use it. The example did use one very broad interest (“Home cinema” global 23 M) as a single-interest test, expecting \~5 M in target country. So that was deemed acceptable.

**In conclusion,** the reach estimation logic combines Facebook’s own estimation API (for accuracy) with heuristic adjustments to keep each targeting group in the desired range. The algorithm will iterate between interest selection and reach checking. By the end, for each column we will have an estimated reach \~4–5 M (with some tolerance), or else that column gets flagged for manual review with an explanation (e.g., “Column 12 only \~800 k reach – niche too small, consider broadening or accept niche”). Marketers are advised to double-check final audience sizes in Ads Manager as well, since the API provides estimates – we noted in our internal guide: “Always check the final audience size after applying the narrow in Facebook Ads Manager; it should fall in the 4–5 M sweet spot”.

---

### 3.5  Performance & Rate-Limit Strategy

* **Batching** – 12 specs = 1 batch request → counts as one call against the 60-score / 300 s throttle.
* **Caching** – Reduces duplicate estimates by ≈ 70 % on iterative edits.
* **Exponential Back-off** – 1 s → 2 s → 4 s on HTTP 429 or error-17, then re-batch.
* **Async Front-end** – UI streams log lines via SSE/WebSocket: “Column 7 estimated at 4.3 M – OK”. No blocking spinners.

---

### 3.6  Fallback “Draft Mode” (Optional)

If delivery-estimate is unreachable and the user opts in, the tool shows per-interest upper-bounds as grey placeholders and watermarks the export **SIZE NOT VALIDATED — DO NOT LAUNCH**. A background worker keeps retrying until real estimates overwrite the draft sizes.

---

### 3.7  Quality Gates Before Export

| Gate              | Rule                                     | Action on Fail               |
| ----------------- | ---------------------------------------- | ---------------------------- |
| **Style Mix**     | 4 columns each of Styles 1, 2, 3         | Block export                 |
| **Duplicates**    | Interest ID unique across sheet          | Auto-dedupe then re-estimate |
| **Reach Window**  | 4 M ≤ midpoint ≤ 5 M                     | Flag Manual Review           |
| **Targetability** | `/targetingvalidation` true for every ID | Drop invalid IDs + re-loop   |
| **Logging**       | Each spec + response persisted 30 d      | Audit-ready                  |

Once all gates pass (or manual flags acknowledged), the sheet is rendered (Excel + PDF) and delivered for immediate import into Ads Manager.

**Outcome:** Marketers receive a 12-column AT2.0 sheet whose audience sizes are verified by the very engine that determines delivery, ensuring predictable learning phases and budget efficiency.


## 4. Integration into a Web Application (Postman to Production)

Designing this algorithm involves not only the logic but also integrating it into a user-friendly tool. Here we outline how to go from using Postman for testing the Marketing API to a deployed web app that marketers can use with minimal friction.

* **Development Stack & Environment:** A suitable stack for this kind of tool is a Python backend (for example, using **FastAPI**) coupled with a lightweight frontend (perhaps a single-page app in **React** for the UI). FastAPI would handle the API calls to Facebook and the core logic, while the React frontend provides the interface for uploading the CSV and downloading the results. This separation allows us to maintain clear code (e.g. Python for data processing, JavaScript for UI) and is aligned with the preferences stated.

* **Postman Collection to Code:** Initially, we can use Postman to develop and debug the required Graph API calls (for targeting search, suggestions, etc.), using a test access token. Once the calls and parameters are confirmed (with correct fields and data returning), we translate those into code. For example, in Python we might use the `requests` library to call the Graph API endpoints, or use Facebook’s Business SDK for Python (which provides wrappers for these endpoints). The Postman environment variables (like `{{account_id}}`, `{{access_token}}`) would correspond to config variables or inputs in our app. We must ensure to securely store the access token (as it’s sensitive). The **Graph API Explorer** and Facebook’s official Postman collection can aid in this process, showing sample requests.

* **User Authentication & Tokens:** Since this is an internal tool, we likely use a System User access token or a long-lived token tied to our business. We need to have the token with the right permissions (ads\_management). In a production web app, we wouldn’t expose the token on the frontend. Instead, the token (or multiple tokens) would be stored on the backend (e.g., as an environment variable or in a secure vault). The backend would append it to each API call. If the tool will be used for multiple ad accounts or by multiple users, we might implement OAuth so each user can authorize their ad account, but given the scope, it sounds like an internal tool for one team’s accounts, so one token is fine.

* **API Schema & Backend Logic:** We design an internal API endpoint (on our FastAPI server) to handle the process. For example, a `POST /build-targeting-sheet` endpoint could accept the input data. The input could be a CSV file uploaded (which the frontend can send as multipart form data) or even just a JSON with an array of interests if the UI collects it differently. The backend then:

  1. Parses the CSV to get a list of interest names (and possibly any provided categories or notes).
  2. For each interest name, it might call `targetingsearch` to get the up-to-date interest ID and audience size. (Alternatively, if the CSV already had the interest ID and global size, we could use the ID directly and maybe just update the size via a quick lookup or reachestimate by country).
  3. Augment the interest list by calling `targetingsuggestions` for certain seeds (the backend knows the 12 categories, so it can decide which seeds to use as described in section 2).
  4. Run the classification logic to assign interests to columns (section 5 discusses how to do this with NLP/rules).
  5. Assemble each column’s interest grouping. This might involve deciding how many interests to put in OR for that column, whether to use a narrow interest, etc. This step uses the style rules and may involve sorting interests by size (e.g. to ensure adding up smaller ones).
  6. Estimate reach for each column. This could be done with the reachestimate API in a loop for all 12 columns. We should be careful here: making 12 sequential API calls to reachestimate could be slow (\~several seconds each perhaps). We might do them in parallel if the SDK supports async, or optimize by skipping if we’re confident (but better not). We could also consider **caching** reach results for recurring combos. Realistically, combos will vary per product, so caching might not help much unless rerunning for the same product multiple times.
  7. Adjust any columns out of range, as described. Possibly loop back to step 5 if adjustments require altering the interest composition.
  8. Format the final results into a structured output. Likely we’ll create a **DataFrame or similar structure with 12 columns**, each column having a list of interests (and maybe the narrow interest separated). We then write this to an Excel file. Libraries like **pandas** or **openpyxl/xlsxwriter** can be used to generate the .xlsx. We include a second sheet or a notes section for any flags (e.g. mark columns that need review in red or add a note cell).
  9. Optionally, generate a PDF summary. This could be done by preparing an HTML report of the results and using a tool like WeasyPrint or ReportLab to render PDF. The summary might list each column, its targeting style, the interests included, and the estimated reach. This is a “nice-to-have” for explaining the strategy to stakeholders.
  10. Return the output to the user. The backend can respond with the file download (or a URL to download). If using a React frontend, the response might be a blob of the file or a link. The user clicks and gets the Excel and PDF.

* **Front-End UI/UX:** The interface is very simple: a single-page where the user can upload or drag\&drop the CSV of seed interests. There may be a couple of settings like selecting the target country (default U.S.), or a button to confirm “Build Targeting Sheet”. Once triggered, the front-end shows a loading indicator and possibly a live log of what’s happening (since in the Upwork spec they mentioned showing logs of each API call for transparency). This could be implemented by the backend streaming log messages via WebSocket or the front-end polling for logs. Logs might include messages like “Fetched ID for ‘Home Theater Forum’ (ID 600..., size \~50k)” or “Retrieved 10 suggestions for seed interest ‘Home theater’”. These help the user trust the process and debug if something goes wrong (for example, if a certain interest wasn’t found or suggestions came back empty, the log would show it). After completion, the UI presents a download link for the Excel (and PDF if provided).

* **Caching & Data Storage:** If this tool will be used frequently or for multiple products, implementing caching is important. We can maintain a cache (in-memory or a simple database table) of interest search results and suggestion results. For instance, if “Netflix” interest was searched once and we got ID and size, store that. Next time any project uses “Netflix”, we skip the API search and reuse the info (maybe refresh the size via reach estimate if we want up-to-date, though interest sizes don’t fluctuate wildly in short term). We could also cache suggestion outputs: e.g., suggestions for “Home theater” might always return a similar set; cache it to avoid hitting API repeatedly. A basic caching strategy can use keys like “search\:Netflix” or “suggestions\:HomeTheater” and expiry of say 1 day or 1 week. This will reduce API calls and speed up the tool, especially if many seeds or overlapping seeds between projects.

* **Error Handling & Edge Cases:** The backend should handle common issues:

  * Expired Access Token: If our token expires or is revoked, API calls will return an OAuth error. We should catch this and alert that re-authentication is needed (developer goes and refreshes token).
  * API Errors/Throttling: If the API returns a rate limit error, we should implement an automatic backoff. For example, if a call fails with error 80004 (rate limit) or a 429 status, pause for a few seconds and retry. We might also proactively add a small delay between bursts of calls to avoid hitting the short-term limit.
  * Missing or Invalid Interests: If an interest from CSV isn’t found via targetingsearch (maybe a typo or it’s not targetable), we can log a warning “Interest X not found or not available” and skip it or mark it for manual review.
  * File handling: ensure the upload can parse various CSV formats (maybe accept .xlsx as well for convenience).
  * Concurrency: If multiple users run the tool at the same time, ensure the backend can handle it (FastAPI can with async). If using the same token for all, that’s fine but note that all calls count to the same rate limit. We might queue requests if needed to avoid collisions.

* **Deployment:** Package the application in a Docker container for easy deployment. This container would contain the Python environment with necessary libraries (FastAPI, requests, pandas, etc.) and perhaps a lightweight web server (Uvicorn for FastAPI). We’d also containerize the React front-end (or serve it statically from the same app). Using Docker ensures a **one-click deployment** to services like AWS (e.g. ECS or a simple EC2) or platforms like Render.com. This makes it easy to spin up or move the app without environment issues. We’ll also include in our deployment configuration the needed environment variables (access token, Facebook App ID if needed, etc.).

* **Security and Privacy:** Since this is an internal tool, we likely don’t have external users, but we still protect the access token and any data. The interest data isn’t personal data (it’s all aggregate info), so no major privacy issue there. But we should ensure the server is not exposing any open endpoints that could be abused by outsiders (perhaps restrict it to our VPN or secure with a basic auth if needed).

* **Monitoring & Maintenance:** We should log usage of the tool (how often it’s run, how long it takes, any errors) so we can monitor performance. If Facebook changes their API (for example, deprecating an endpoint or fields), we need to update the code accordingly. Keeping an eye on the [Developer Changelog](https://developers.facebook.com/docs/graph-api/changelog) for Marketing API is important to anticipate breaking changes (e.g. the 2021 change to audience\_size we discussed).

By following this integration plan, we ensure that what we tested manually in Postman (queries to `targetingsearch`, `targetingsuggestions`, etc.) is now part of an automated pipeline in a web app. The result is a user-friendly solution where a marketer just uploads a list of ideas and downloads a fully baked targeting plan, with all the heavy lifting done via the Meta API in the backend.

## 5. Interest Classification & Taxonomy Mapping Strategy

One of the more complex pieces is how to automatically classify arbitrary interests into the 12 predefined categories of Algorithmic Targeting 2.0. This essentially requires an **interest classifier** that maps each interest to one of the 12 columns (or flags it if it doesn’t fit any well). We propose a hybrid approach combining **rule-based filtering** with **NLP-based semantic similarity** to achieve this:

* **Rule-Based Classification by Keywords and Known Entities:** Many interests can be categorized by simple cues in their names or known context:

  * *Magazine/Media (Column 3 “Magazine Flex”):* If the interest name corresponds to a known magazine, newspaper, or online publication, classify here. For instance, interests like *“Wired”*, *“TechCrunch”*, *“What Hi-Fi?”* clearly are tech media. Clues include words like *Magazine*, *Journal*, or known titles. We can maintain a list of known publications in the niche (perhaps scraped from Wikipedia or provided by the user). Also, if the interest’s Facebook topic/path suggests it’s in *News & Entertainment* category, that’s a hint (Facebook’s `topic` field for an interest might say “News and Entertainment” for media-related interests, as seen in some cases).
  * *Websites & Keywords (Column 4):* These are essentially interest that are websites or generic tech keywords. E.g. *“CNET”*, *“Engadget”*, *“Digital Trends”* are websites/blogs. They often overlap with media, but we might distinguish that Column 3 is more for traditional or niche media, while Column 4 might include broader tech websites or generic tech terms not covered elsewhere. Rule: If interest is a tech website or a broad tech term that doesn’t fall under a magazine or brand, it might go here. Presence of .com (rare in interest names) or being known as an online platform could qualify.
  * *Niche Categories – Product Brands (Column 5):* This column is for **specific brands or product types in the niche**. In the projector example, *Epson, BenQ, Optoma, Anker* (brands that make projectors) were classified here. To detect these, we could have a known list of major brands in the niche (perhaps the user or marketer can provide those seeds). Alternatively, we use *targetingsearch* on the niche keyword (“projector”) and see if brand names appear in results or suggestions. Often, brand interests have the broad category `Business and industry` in their path (because they’re companies). The `topic` field “Business and industry” or related could hint an interest is a company/brand. So the classifier can say: if an interest’s topic is Business (or its name matches known electronics companies), and it’s relevant (projector or related electronics), put in Niche Categories.
  * *Passionate Interest Groups (Column 6 “Passionate Flex”):* These could include **Facebook Groups or community names**, or generic enthusiast terms. In the example: *“Home Theater Enthusiasts (Facebook Group)”*, *“Audiophile”*, *“I ❤ Movies”*. Clues: interest name might explicitly contain words like *“Enthusiasts”*, *“Fans”*, *“I love…”*, etc., indicating a community or passion. Some interests come from Facebook Pages that were groups or community pages. The `disambiguation_category` in search results might show “Facebook Group” or similar for such interests (as indicated with Home Theater Enthusiasts). We can also include high-affinity hobby terms here if they denote strong passion (like *“Audiophile”* clearly is a self-identification of enthusiasts). So rule: if name contains group-like terms or generally describes a passionate hobbyist identity, classify as Passionate Flex.
  * *TV Shows & Influencers (Column 7):* This includes **popular shows, personalities, or influencer names** related to the niche. E.g. *“Marques Brownlee (MKBHD)”*, a YouTube tech reviewer, was used. Also could include, say, a famous TV show about home improvement or a movie series (if relevant to big screens). Clues: `disambiguation_category` or `path` might show “Public Figure”, “Entertainment” etc. In the Adele example from targetingsearch, entries had categories like “Musician/Band”, “Public Figure”. For our use-case, if an interest is a person (especially tech reviewers, influencers) or a show (maybe *“Home Improvement (TV series)”* or a Sci-fi movie that projector enthusiasts like), it goes here. Name recognition or manual lists might be needed. Possibly the suggestions API might surface some (e.g. seeding with a tech interest might suggest a famous person).
  * *Core “Neck” (Column 8):* This is the **core broad keyword of the niche** – essentially the one or two word term that directly describes the niche. In example, *“Home cinema”* was chosen. Others could be *“Home theater”*, *“Projector”* itself, etc. Likely we identify this by taking the main product keyword (like “Projector”) or something synonymous that has a large audience. Usually, the marketer knows this term. The classifier can designate the very broad interest as Core. Possibly if an interest’s audience is huge (20M+ global) and it literally matches the niche name, that’s the core.
  * *Related Niche (Column 9):* These are **adjacent interests** – not about the product itself, but something that overlaps in audience. The example used *“Video Games”* for a projector product, reasoning that gamers might be interested in projectors for large-screen gaming. For classification, this is a bit open-ended. It requires some creativity or external knowledge. One approach: look at broad interests that aren’t directly about the product but the target audience likely engages in. We might find these via suggestions: e.g. if we input “Projectors” to suggestions, it might return “Video game console” or “Streaming media” etc. The classifier can identify if an interest is not directly in the main niche but logically related. Possibly using word vectors: if the interest is semantically not too far but not a subset of the niche concept. For automation, we might maintain a list of common related categories for tech products (like gamers, photographers, movie buffs for a projector). A dynamic approach is using the *affinity insights* – e.g. using tools or data from Audience Insights (when it existed) to see what other categories fans of projectors like. This is perhaps where an **NLP embedding approach** can help: embed all candidate interests and see which one is an outlier cluster that is separate from the main niche cluster – those outliers might be “related but not core” interests, thus fitting this category.
  * *Buyers Flex (Column 10):* Interests that indicate **buyer behavior or marketplaces** in the niche. The example had *“Indiegogo”*, *“B\&H Photo Video”*, *“Newegg”* – these are e-commerce or crowdfunding platforms known to gadget enthusiasts. Generally, this category captures pages that real buyers of such products might follow. Clues: interest is an online store (especially electronics store), a tech marketplace, or a gadget shopping behavior. Facebook has a behavior “Engaged Shoppers” which could be considered, but since we focus on interests: names of retailers (outside of huge ones like Amazon/Walmart which are too broad) fit here. We can maintain a list of known specialty retailers for electronics. Also, if an interest’s `name` or `description` indicates it’s a retail/business (for instance, B\&H Photo’s interest likely has topic “Shopping and fashion” or “Business” in Facebook’s taxonomy), that’s a clue.
  * *Affinity Flex (Column 11):* Interests representing **high-affinity pages** – things that aren’t obviously about the product but data shows people interested in the product often like these. In the example: *“Photography”*, *“Gadget Geek”*, *“Netflix”* narrowed by “Projectors”. How to classify? This is somewhat similar to Related Niche, but Affinity Flex often includes broader interests that a lookalike or audience insights would reveal. It could be general tech enthusiasm (Gadget Geek sounds like a broad page for gadget lovers), or tangential but popular interests (Netflix for people who like watching content – likely projector owners watch a lot of Netflix). The classifier might identify these by a combination of traits: interest with very large audiences that are not specific to our niche but have contextual relevance. Possibly by process of elimination: after picking out all clearly niche and directly related interests for other columns, some remaining ones that are broad/pop-culture might fall here. Another approach: If we had a way to get “affinity scores” (which we don’t via API easily now), we’d use that. Instead, we may rely on domain knowledge or suggestions. For example, if suggestions for “Home theater” returned “Netflix”, that indicates a high affinity. So if an interest comes via suggestion from a core interest and it’s broad, treat it as an affinity interest.
  * *Store “Light” Flex (Column 12):* Interests in **specialty retailers or smaller chain stores** relevant to the niche. The example gave *“Crutchfield”* (a well-known specialty AV retailer). To classify, look for interest names that are store or brand names known for selling such products. We avoid giant retailers (like Amazon, Walmart) because those audiences are huge and not specific. So essentially a subset of Buyers category but focusing on *smaller scale retailers*. Our classifier can have a list of such stores (Crutchfield, Best Buy (though Best Buy is quite large), maybe regional electronics stores). If a known store is found via search/suggestions, put it here. If none found or not applicable, this column might sometimes be empty or merged with Buyers flex. But in AT2.0 they specifically call it out to ensure even small store audiences are tested.

* **NLP Embeddings & Similarity:** While rules handle many cases, we can augment with an NLP approach for flexibility:

  * We can obtain text embeddings for each interest name (and perhaps its description or related context). For instance, use a model like **Facebook’s FastText** or a general model like **BERT sentence embeddings** to convert interest names into vectors in a semantic space.
  * We also create representative “category vectors” for each of the 12 columns. This could be done by **hand-picking a few prototypical words or example interests for each category** and averaging their embeddings. For example, for Magazine Flex, prototypes might be words like “magazine”, “journal”, plus known magazine names; for Buyers Flex, words like “shopping”, “store”, “e-commerce”, etc. Alternatively, if we have a training set (perhaps from prior projects) where some interests were labeled by category, we could train a simple classifier on those embeddings.
  * When a new interest is encountered, we compute its similarity to each category’s prototype. The interest gets assigned to the category with the highest similarity score, provided it exceeds a certain threshold. If the scores are low or ambiguous, we might rely on the rule-based method or default to a logical category.
  * For example, an interest “AVS Forum” (Audio Video Science Forum) might not explicitly say “forum” in name (though it does say Forum). Our rules might catch “Forum” as a passionate community marker (Column 6). An embedding approach would place “AVS Forum” near words like “forum, enthusiast” etc., confirming it belongs in Passionate Flex.
  * Another example: “Netflix” might confuse a purely rule-based system (it’s a brand, but not a projector brand, it’s entertainment content). The embedding might show “Netflix” is similar to other entertainment terms, and if our Affinity category prototypes include “Netflix, movies, streaming”, it will fit there.
  * We should be cautious: embedding short names can sometimes be off if the name is obscure. However, combining name with path/topic could help. For instance, “Optoma” alone might not be widely known in the embedding model, but if we know from the path it’s an Electronics brand, that helps.

* **Utilizing Facebook’s Taxonomy (`path` & `topic`):** As mentioned, the Graph API provides a `path` array and a `topic` for interests. We can use this data:

  * If `topic` (the broad category) is something like “Technology” or “Entertainment”, that guides placement. E.g., interest with topic “Entertainment” could be a show or movie (Column 7 or 9 or 11 depending on context). Topic “Shopping” could indicate a retailer or brand (Column 10 or 12). Topic “Hobbies and Activities” might indicate an enthusiast interest (Column 6).
  * The second element in `path` often is a mid-level category, which might correlate with our columns. For instance, if path is \["Interests","Business and industry","Photography"], that interest might be photography (affinity if we consider projector niche). If path is \["Interests","Technology","Consumer Electronics"], and the interest name is a brand, that likely goes to Niche Categories.
  * We should gather some examples of how interests in each of our desired columns appear in Facebook’s categorization. Noting that *Affinity Flex* often will include interests in seemingly unrelated categories (like “Netflix” would be under Entertainment -> TV/Movies). So path alone isn’t enough to decide our custom category, but it provides hints combined with name analysis.

* **Feedback Loop and Manual Override:** The system could allow for manual review or override of classifications. Since the final goal is to save time but not at the expense of logic, we might present the initial classification to the user in the log or in the summary, so they can see if anything looks out of place. For example, if the algorithm mis-classified something, the user can move it in the sheet before launching ads (or re-run with that interest categorized differently via some input). As we gather more data (which columns perform well, etc.), the classification rules can be refined.

* **Verification with Examples:** We can test our classifier on the example sheet itself (where we know the correct categories). For instance, feed the 30+ interests from the Magcubic projector example through our classifier and see if they fall into columns as expected. If some don’t, adjust the rules or training data. This ensures the classifier logic aligns with the original methodology.

* **Interests Outside the 12 Categories:** If the API suggestion returns a very interesting target that doesn’t clearly fit one of the 12 (which is rare because the categories are broad), we might drop it or consider if it actually signals a need to tweak categories. The 12 are supposed to cover most angles, but occasionally you might get something like a demographic or behavior suggestion. Since our scope is interests only, we likely ignore behaviors like age, etc. But if an interest is truly odd, we can log it for manual consideration.

* **Example of Classifier in Action:** Suppose the suggestions API returns “Dolby Atmos” as an interest (a surround sound technology). How do we classify it? Possibly as part of *Keyword Mix* (if it’s small) or *Related Niche* (since it's a related tech concept). Our rule might not explicitly cover this. But an embedding could place “Dolby Atmos” near “Surround sound” or “Home theater” because of semantic closeness. If we have Column 2 as broad keywords >1M and Column 1 as small keywords, we decide by size: if Dolby Atmos has <1M audience, throw it in Keyword Mix <1M (Column 1); if >1M, maybe Keyword Mix >1M (Column 2). Indeed, we can incorporate **size-based rules** as well: the two “Keyword Mix” columns are essentially split by size (<1M vs >1M). So the classifier also checks the audience size of an interest. If below 1M and not otherwise categorized, it likely belongs to Column 1. If above 1M and is a generic term, Column 2.

  Similarly, if an interest name matches a known retailer and its size is moderate, and if we already have bigger retailers in Buyers Flex, we might decide to put that moderate one in Store Light Flex.

* **Leveraging External Data:** We could use external sources to enhance classification. For example:

  * Scrape Wikipedia or do a quick search: if an interest name has a Wikipedia page, parse the description to see keywords like “X is a magazine…” or “X is a company…”. This could automatically tell us category (magazine, brand, person, etc.). This is an NLP task itself but could improve accuracy for less obvious names. This should be done selectively due to time constraints per run.
  * Use a knowledge graph: if we had access to something like DBPedia or an entity recognition API, feeding the interest name might return a type (like *Person*, *Website*, *Corporation*, *Film*, etc.). Those types map to our categories fairly well.

* **Continuous Learning:** Over time, if the tool is used for many products, we can accumulate a dataset of interests labeled by the column (because the tool’s output effectively labels them). This data can train a more direct machine learning model (multiclass classifier). For instance, a simple approach: represent each interest by features (name text, maybe path topics, size) and train a decision tree or SVM to predict the column. Initially we won’t have that data, so we rely on rules and general NLP, but it’s a future improvement.

In summary, the interest classification will combine **explicit rules (keywords, known lists, size thresholds)** and **NLP-based similarity** to map interests into the AT 2.0 columns. We utilize Facebook’s own categorization (`path`/`topic`) to inform this mapping, ensuring we align with how an interest is contextually understood (e.g., knowing an interest is a business versus a hobby). By doing so, the algorithm can auto-fill the 12 columns in a way that makes intuitive sense. According to the methodology, keeping interests tightly relevant in each column is key, so our classifier emphasizes thematic coherence. If an interest does not closely align with a column’s theme, it should not be forced in; instead it could be set aside or used to inspire a new angle (though in AT 2.0, we stick to the 12 standard angles).

Finally, after classification and sheet assembly, we ensure **no overlap of interests between columns** (deduplication) and adhere to using each interest at most once (or twice at most in rare cases) in the whole plan. The result is a well-organized targeting sheet tailored to the product, leveraging real-time data and smart categorization to maximize the chances of finding winning audiences.

**Sources:**

* Facebook Marketing API – *Detailed Targeting* reference and changelogs
* Stack Overflow – Examples of Graph API usage for interest targeting (search, suggestions, reach estimate)
* *Algorithmic Targeting 2.0* internal guide – methodology and example for 12-column structure
* Upwork Project Spec – functional requirements for automating AT 2.0 (Interest Picker tool)
* Facebook Dev Forum & GitHub Wiki – usage of targeting endpoints and fields
* Facebook Ads Manager Experience – interest suggestion behavior and audience size considerations



