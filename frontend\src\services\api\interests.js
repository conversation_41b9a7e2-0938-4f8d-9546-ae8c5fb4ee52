/**
 * Interest API Service
 * Handles all interest-related API calls
 */

import apiClient from './client.js';

export const interestApi = {
  /**
   * Search for interests by keyword
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Search results
   */
  async search(query, options = {}) {
    const params = {
      q: query,
      type: options.type || 'adinterest',
      limit: options.limit || 1000,
      ...options
    };

    return apiClient.get('/targeting/search', params);
  },

  /**
   * Bulk search for multiple keywords
   * @param {Array<string>} keywords - Array of keywords to search
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Bulk search results
   */
  async bulkSearch(keywords, options = {}) {
    const data = {
      keywords,
      country_code: options.countryCode || 'US',
      limit_per_keyword: options.limitPerKeyword || 10,
      ...options
    };

    return apiClient.post('/targeting/bulk-search', data);
  },

  /**
   * Get interest suggestions based on seed interest
   * @param {string} interestId - Seed interest ID
   * @param {Object} options - Suggestion options
   * @returns {Promise<Object>} Suggestions
   */
  async getSuggestions(interestId, options = {}) {
    const params = {
      interest_id: interestId,
      limit: options.limit || 1000,
      ...options
    };

    return apiClient.get('/targeting/suggestions', params);
  },

  /**
   * Get bulk suggestions for multiple seed interests
   * @param {Array<string>} interestIds - Array of seed interest IDs
   * @param {Object} options - Suggestion options
   * @returns {Promise<Object>} Bulk suggestions
   */
  async getBulkSuggestions(interestIds, options = {}) {
    const data = {
      interest_ids: interestIds,
      country_code: options.countryCode || 'US',
      limit_per_seed: options.limitPerSeed || 20,
      deduplicate: options.deduplicate !== false,
      ...options
    };

    return apiClient.post('/targeting/bulk-suggestions', data);
  },

  /**
   * Browse taxonomy/categories
   * @param {Object} options - Browse options
   * @returns {Promise<Object>} Taxonomy data
   */
  async browseTaxonomy(options = {}) {
    const params = {
      parent_id: options.parentId,
      limit_type: options.limitType || 'interests',
      locale: options.locale || 'en_US',
      ...options
    };

    return apiClient.get('/targeting/taxonomy', params);
  },

  /**
   * Browse categories
   * @param {Object} options - Browse options
   * @returns {Promise<Object>} Categories
   */
  async browseCategories(options = {}) {
    const params = {
      category_type: options.categoryType || 'adtargetingcategory',
      targeting_class: options.targetingClass || 'demographics',
      ...options
    };

    return apiClient.get('/targeting/browse', params);
  }
};

export default interestApi;