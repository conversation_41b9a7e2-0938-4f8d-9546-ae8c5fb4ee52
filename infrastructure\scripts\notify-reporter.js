/**
 * Jest reporter that integrates with the notification system
 */

const notify = require('./notify');

class NotifyReporter {
  constructor(globalConfig, options = {}) {
    this._globalConfig = globalConfig;
    this._options = options;
  }

  onRunStart() {
    notify.test('Starting test suite');
  }

  onTestResult(test, testResult) {
    if (testResult.numFailingTests > 0) {
      const failedTests = testResult.testResults.filter(t => t.status === 'failed');
      failedTests.forEach(test => {
        notify.error(`Test failed: ${test.fullName}`, {
          context: 'Test Failure',
          skipInCI: false
        });
      });
    }
  }

  onRunComplete(contexts, results) {
    const { numFailedTests, numPassedTests, numPendingTests, testResults, startTime } = results;
    const endTime = Date.now();
    
    notify.testResults({
      passed: numPassedTests,
      failed: numFailedTests,
      skipped: numPendingTests,
      duration: endTime - startTime
    });

    // Call the custom callback if provided
    if (this._options.onTestResults) {
      this._options.onTestResults(results);
    }
  }
}

module.exports = NotifyReporter;
