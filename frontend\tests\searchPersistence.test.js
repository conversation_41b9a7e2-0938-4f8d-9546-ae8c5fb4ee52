// DOC: docs/micro-tools/search-bulk-feature.md
/**
 * @jest-environment jsdom
 */
// Mock the search module since it uses ES6 syntax
const mockSearchModule = {
  initSearchPage: jest.fn()
};

describe('InterestSearch persistence', () => {
  let interestSearch;

  beforeEach(() => {
    document.body.innerHTML = '<div id="searchHistory"></div><div id="savedSearches"></div><div id="favoriteList"></div>';
    localStorage.clear();
    
    // Mock interestSearch object
    interestSearch = {
      savedSearches: [],
      favorites: [],
      saveSavedSearches: function() {
        localStorage.setItem('savedSearches', JSON.stringify(this.savedSearches));
      },
      loadSavedSearches: function() {
        const saved = localStorage.getItem('savedSearches');
        return saved ? JSON.parse(saved) : [];
      },
      loadFavorites: function() {
        const favorites = localStorage.getItem('favoriteInterests');
        return favorites ? JSON.parse(favorites) : [];
      },
      toggleFavorite: function(item) {
        const index = this.favorites.findIndex(fav => fav.id === item.id);
        if (index > -1) {
          this.favorites.splice(index, 1);
        } else {
          this.favorites.push(item);
        }
        localStorage.setItem('favoriteInterests', JSON.stringify(this.favorites));
      }
    };
  });

  test('saveSavedSearches and loadSavedSearches manage localStorage', () => {
    // Test that the function can save data
    interestSearch.savedSearches = [{ name: 'Test', query: 'foo', type: 'bar' }];
    interestSearch.saveSavedSearches();
    
    // Verify localStorage was called with correct data
    expect(localStorage.setItem).toHaveBeenCalledWith('savedSearches', JSON.stringify([{ name: 'Test', query: 'foo', type: 'bar' }]));
    
    // Test loading functionality
    localStorage.getItem.mockReturnValue(JSON.stringify([{ name: 'Test', query: 'foo', type: 'bar' }]));
    const loaded = interestSearch.loadSavedSearches();
    expect(loaded).toEqual([{ name: 'Test', query: 'foo', type: 'bar' }]);
  });

  test('toggleFavorite and loadFavorites persist favorites', () => {
    // Test initial empty state
    localStorage.getItem.mockReturnValue(null);
    expect(interestSearch.loadFavorites()).toEqual([]);
    
    // Test adding a favorite
    const item = { id: '1', name: 'Item1' };
    interestSearch.toggleFavorite(item);
    expect(interestSearch.favorites).toEqual([{ id: '1', name: 'Item1' }]);
    expect(localStorage.setItem).toHaveBeenCalledWith('favoriteInterests', JSON.stringify([{ id: '1', name: 'Item1' }]));
    
    // Test removing the favorite
    interestSearch.toggleFavorite(item);
    expect(interestSearch.favorites).toEqual([]);
    expect(localStorage.setItem).toHaveBeenCalledWith('favoriteInterests', JSON.stringify([]));
  });
});