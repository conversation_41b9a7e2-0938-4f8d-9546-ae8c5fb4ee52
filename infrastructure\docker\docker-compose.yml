version: '3.8'

services:
  backend:
    build: 
      context: ../../
      dockerfile: infrastructure/docker/Dockerfile.backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
    volumes:
      - ../../backend:/app
      - ../../shared:/shared
    depends_on:
      - redis

  frontend:
    build:
      context: ../../
      dockerfile: infrastructure/docker/Dockerfile.frontend
    ports:
      - "3000:3000"
    volumes:
      - ../../frontend:/app
      - ../../shared:/shared
    environment:
      - NODE_ENV=development

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data: