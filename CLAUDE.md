# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Memory Imports

@docs/process/documentation-driven-development.md

## Project Overview

TargetWise is a professional fullstack application for building 12-column Algorithmic Targeting sheets using Meta Marketing API. It implements the Algorithmic Targeting 2.0 methodology with a 4-4-4 rule for optimizing Facebook ad campaigns.

## Architecture

### Tech Stack
- **Frontend**: Vanilla JavaScript with ES6 modules, Vite build tool, custom CSS design system
- **Backend**: FastAPI (Python 3.10+) with async support, sentence-transformers for ML
- **Infrastructure**: Docker support, Redis for caching, PostgreSQL/SQLite for data
- **API**: RESTful with OpenAPI documentation at `/docs`

### Key Architectural Decisions
- **API-First Design**: Frontend and backend communicate exclusively through RESTful APIs
- **Module-Based Frontend**: Each page is a self-contained ES6 module with init functions
- **Service Layer Pattern**: Backend uses service classes for business logic separation
- **Caching Strategy**: Redis caches API responses with configurable TTL
- **ML Integration**: Interest classification uses sentence-transformers for semantic search

## Development Commands

### Frontend Development
```bash
cd frontend
python3 server.py          # Start dev server on :8080
npm run dev               # Alternative: Vite dev server
npm run build            # Production build
npm run test             # Run Jest unit tests
npm run test:e2e         # Run Playwright E2E tests
npm run lint             # ESLint checking
npm run format           # Prettier formatting
```

### Backend Development
```bash
# From project root
python3 run.py           # Start FastAPI server on :8000
python3 -m pytest        # Run all tests
python3 -m pytest tests/backend/services/test_interest_classifier.py  # Run specific test

# Using Make (from infrastructure/)
make install             # Install dependencies
make test                # Run tests
make test-cov           # Run tests with coverage
make lint               # Run flake8 and mypy
make format             # Format with black and isort
```

### Running Both Servers
1. Terminal 1: `python3 run.py` (backend on :8000)
2. Terminal 2: `cd frontend && python3 server.py` (frontend on :8080)

Note: If backend dependencies aren't installed, the frontend includes a mock API server:
```bash
cd frontend
python3 mock-api-server.py  # Mock API on :8000
```

## Testing

### Frontend Testing
- Unit tests: `frontend/tests/*.test.js` - Component and utility tests
- E2E tests: `frontend/tests/e2e/*.spec.js` - Full user flow tests
- Run single test: `npm test -- tableFilter.test.js`

### Backend Testing
- Unit tests: `tests/backend/` - Service and route tests
- Test structure mirrors source structure
- Run single test: `python3 -m pytest tests/backend/services/test_interest_classifier.py -v`

### Key Test Files
- Interest classification: `tests/backend/services/test_interest_classifier.py`
- API routes: `tests/backend/test_routes.py`
- Frontend search: `frontend/tests/search.test.js`
- E2E search flow: `frontend/tests/e2e/searchFlow.spec.js`

## Code Organization

### Frontend Structure
- `/src/pages/` - Page components (TargetWiseMain, Dashboard, etc.)
- `/src/services/api/` - API client and service modules
- `/src/styles/design-system/` - CSS variables, components, utilities
- `/src/app.js` - Main router and initialization

### Backend Structure
- `/api/v1/` - API route handlers organized by feature
- `/services/` - Business logic (interest search, suggestions, etc.)
- `/models/` - Data models and schemas
- `/utils/` - Shared utilities (caching, errors, monitoring)

### Key Services
- `InterestSearchService`: Facebook interest search with caching
- `SuggestionService`: ML-powered interest suggestions
- `ReachEstimationService`: Audience size calculations
- `TargetingBuilder`: 12-column sheet generation

## API Endpoints

Primary endpoints:
- `GET /api/v1/search/interests` - Search Facebook interests
- `POST /api/v1/suggestions` - Get ML-powered suggestions
- `POST /api/v1/targeting/build` - Generate targeting sheet
- `GET /api/v1/admin/config` - Admin configuration

Full API documentation available at http://localhost:8000/docs when backend is running.

## Performance Considerations

1. **Frontend Loading**: Pages use dynamic imports, CSS is modular
2. **API Caching**: Redis caches responses (60s default TTL)
3. **Batch Operations**: Bulk endpoints for processing multiple items
4. **Async Processing**: FastAPI uses async/await for I/O operations

## Common Issues

1. **Slow Loading**: Ensure both frontend (:8080) and backend (:8000) servers are running
2. **API Errors**: Check if Redis is running for caching functionality
3. **Import Errors**: Frontend uses absolute paths from root (e.g., `/src/...`)
4. **CORS Issues**: Both servers include CORS headers for development