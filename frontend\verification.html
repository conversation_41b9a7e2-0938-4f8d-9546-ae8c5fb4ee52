<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise - Page Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            margin: 0;
            padding: 40px 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #1e293b;
            margin-bottom: 16px;
        }
        .subtitle {
            text-align: center;
            color: #64748b;
            margin-bottom: 40px;
            font-size: 18px;
        }
        .verification-grid {
            display: grid;
            gap: 24px;
        }
        .page-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        }
        .status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #10b981;
            color: white;
            border-radius: 8px;
            font-weight: 600;
        }
        .check-icon {
            width: 20px;
            height: 20px;
        }
        .features-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        .feature {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
        }
        .feature-icon {
            width: 24px;
            height: 24px;
            background: #e0f2fe;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2563eb;
            flex-shrink: 0;
        }
        .page-link {
            display: inline-block;
            padding: 12px 24px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.2s;
        }
        .page-link:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-top: 16px;
        }
        .comparison-item {
            padding: 16px;
            border-radius: 8px;
            background: #f1f5f9;
        }
        .comparison-item h4 {
            font-size: 14px;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .comparison-item p {
            color: #1e293b;
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ TargetWise Page Verification</h1>
        <p class="subtitle">All pages have been updated to match the exact layout from the provided HTML files</p>

        <div class="verification-grid">
            <!-- TargetWise Main -->
            <div class="page-card">
                <div class="page-header">
                    <h2 class="page-title">TargetWise Main</h2>
                    <div class="status">
                        <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Updated
                    </div>
                </div>
                
                <div class="features-list">
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Header with logo and navigation</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Hero section with gradient</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Info cards grid</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Builder card with forms</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>File upload area</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Loading & success states</span>
                    </div>
                </div>
                
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>Source File</h4>
                        <p>/mnt/c/Users/<USER>/Downloads/targetwise-main-enhanced.html</p>
                    </div>
                    <div class="comparison-item">
                        <h4>Updated File</h4>
                        <p>/frontend/src/pages/TargetWiseMain.js</p>
                    </div>
                </div>
                
                <a href="/" class="page-link">View Page →</a>
            </div>

            <!-- Enhanced Dashboard -->
            <div class="page-card">
                <div class="page-header">
                    <h2 class="page-title">Enhanced Dashboard</h2>
                    <div class="status">
                        <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Updated
                    </div>
                </div>
                
                <div class="features-list">
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Sidebar navigation</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Welcome section with gradient</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Tools grid with cards</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>API counter</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Floating action button</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Decorative circles</span>
                    </div>
                </div>
                
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>Source File</h4>
                        <p>/mnt/c/Users/<USER>/Downloads/enhanced-targetwise-dashboard.html</p>
                    </div>
                    <div class="comparison-item">
                        <h4>Updated File</h4>
                        <p>/frontend/src/pages/Dashboard.js</p>
                    </div>
                </div>
                
                <a href="/dashboard" class="page-link">View Page →</a>
            </div>

            <!-- Interest Search -->
            <div class="page-card">
                <div class="page-header">
                    <h2 class="page-title">Interest Search</h2>
                    <div class="status">
                        <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Updated
                    </div>
                </div>
                
                <div class="features-list">
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Search history sidebar</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Tab container (Single/Bulk)</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Search panel with filters</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Results table</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Category badges</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Action buttons</span>
                    </div>
                </div>
                
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>Source File</h4>
                        <p>/mnt/c/Users/<USER>/Downloads/interest-search-enhanced.html</p>
                    </div>
                    <div class="comparison-item">
                        <h4>Updated File</h4>
                        <p>/frontend/src/pages/InterestSearchEnhanced.js</p>
                    </div>
                </div>
                
                <a href="/search" class="page-link">View Page →</a>
            </div>

            <!-- Interest Suggestions -->
            <div class="page-card">
                <div class="page-header">
                    <h2 class="page-title">Interest Suggestions</h2>
                    <div class="status">
                        <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Updated
                    </div>
                </div>
                
                <div class="features-list">
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Two-column layout</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Seed library with search</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Suggestions panel</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Tab switcher</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Bulk actions bar</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Staging cart button</span>
                    </div>
                </div>
                
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>Source File</h4>
                        <p>/mnt/c/Users/<USER>/Downloads/interest-suggestions-enhanced.html</p>
                    </div>
                    <div class="comparison-item">
                        <h4>Updated File</h4>
                        <p>/frontend/src/pages/InterestSuggestions.js</p>
                    </div>
                </div>
                
                <a href="/suggestions" class="page-link">View Page →</a>
            </div>

            <!-- Interest Pool -->
            <div class="page-card">
                <div class="page-header">
                    <h2 class="page-title">Interest Pool</h2>
                    <div class="status">
                        <svg class="check-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Updated
                    </div>
                </div>
                
                <div class="features-list">
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Stats cards grid</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Collection info banner</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Actions bar</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Filters section</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Data table</span>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">✓</div>
                        <span>Export functionality</span>
                    </div>
                </div>
                
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>Source File</h4>
                        <p>/mnt/c/Users/<USER>/Downloads/interest-pool-enhanced.html</p>
                    </div>
                    <div class="comparison-item">
                        <h4>Updated File</h4>
                        <p>/frontend/src/pages/InterestPool.js</p>
                    </div>
                </div>
                
                <a href="/pool" class="page-link">View Page →</a>
            </div>
        </div>

        <div style="margin-top: 48px; text-align: center;">
            <h2 style="color: #1e293b; margin-bottom: 16px;">✨ All Pages Updated Successfully!</h2>
            <p style="color: #64748b; max-width: 600px; margin: 0 auto;">
                Each page now matches the exact structure, styling, and layout from the provided HTML files. 
                The design system from targetwise-design-system.md has been consistently applied across all pages.
            </p>
        </div>
    </div>
</body>
</html>