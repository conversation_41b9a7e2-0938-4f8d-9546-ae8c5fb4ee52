<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug TargetWise</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
        }
        .debug-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
            margin-bottom: 20px;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="debug-card">
        <h1>🔍 TargetWise Debug Console</h1>
        
        <h2>1. Server Status</h2>
        <div id="server-status">
            <p class="success">✅ Server is responding</p>
        </div>
        
        <h2>2. File Access Test</h2>
        <div id="file-tests">
            <p>Testing file access...</p>
        </div>
        
        <h2>3. CSS Loading Test</h2>
        <div id="css-tests">
            <p>Testing CSS loading...</p>
        </div>
        
        <h2>4. JavaScript Module Test</h2>
        <div id="js-tests">
            <p>Testing JavaScript modules...</p>
        </div>
        
        <h2>5. Console Output</h2>
        <div id="console-output">
            <pre id="console-log"></pre>
        </div>
    </div>
    
    <script>
        const log = document.getElementById('console-log');
        const originalConsole = console.log;
        const originalError = console.error;
        
        // Capture console output
        console.log = function(...args) {
            log.textContent += '[LOG] ' + args.join(' ') + '\n';
            originalConsole.apply(console, args);
        };
        
        console.error = function(...args) {
            log.textContent += '[ERROR] ' + args.join(' ') + '\n';
            originalError.apply(console, args);
        };
        
        // Test file access
        async function testFileAccess() {
            const files = [
                '/src/app.js',
                '/src/styles/index.css',
                '/src/pages/TargetWiseMain.js',
                '/index.html'
            ];
            
            const results = [];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        const contentType = response.headers.get('content-type');
                        results.push(`✅ ${file} - ${response.status} (${contentType})`);
                    } else {
                        results.push(`❌ ${file} - ${response.status} ${response.statusText}`);
                    }
                } catch (error) {
                    results.push(`❌ ${file} - ${error.message}`);
                }
            }
            
            document.getElementById('file-tests').innerHTML = results.map(r => `<p>${r}</p>`).join('');
        }
        
        // Test CSS loading
        async function testCSS() {
            try {
                const response = await fetch('/src/styles/index.css');
                if (response.ok) {
                    const css = await response.text();
                    if (css.includes('@import')) {
                        document.getElementById('css-tests').innerHTML = '<p class="warning">⚠️ CSS contains @import statements that might cause issues</p>';
                    } else {
                        document.getElementById('css-tests').innerHTML = '<p class="success">✅ CSS loads correctly</p>';
                    }
                } else {
                    document.getElementById('css-tests').innerHTML = '<p class="error">❌ CSS failed to load</p>';
                }
            } catch (error) {
                document.getElementById('css-tests').innerHTML = `<p class="error">❌ CSS error: ${error.message}</p>`;
            }
        }
        
        // Test JavaScript module loading
        async function testJS() {
            try {
                console.log('Testing JavaScript module import...');
                const module = await import('/src/app.js');
                console.log('Module loaded successfully:', module);
                document.getElementById('js-tests').innerHTML = '<p class="success">✅ JavaScript module loads correctly</p>';
            } catch (error) {
                console.error('Module loading failed:', error);
                document.getElementById('js-tests').innerHTML = `<p class="error">❌ JavaScript error: ${error.message}</p>`;
            }
        }
        
        // Run all tests
        async function runTests() {
            console.log('Starting debug tests...');
            await testFileAccess();
            await testCSS();
            await testJS();
            console.log('Debug tests completed.');
        }
        
        // Start tests when page loads
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
