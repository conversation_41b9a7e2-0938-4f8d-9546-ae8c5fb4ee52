<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-card">
        <h1>Minimal JavaScript Test</h1>
        <div id="status">Testing...</div>
        <div id="module-test">Module test pending...</div>
    </div>
    
    <script>
        // Basic JavaScript test
        document.getElementById('status').innerHTML = '<span class="success">✅ Basic JavaScript works</span>';
        
        // Test module loading
        async function testModule() {
            try {
                // Try to import a simple module
                const response = await fetch('/src/app.js');
                const text = await response.text();
                
                if (text.includes('import') && text.includes('export')) {
                    document.getElementById('module-test').innerHTML = '<span class="success">✅ Module file exists and contains ES6 syntax</span>';
                } else if (text.includes('<html>')) {
                    document.getElementById('module-test').innerHTML = '<span class="error">❌ Server returning HTML instead of JavaScript</span>';
                } else {
                    document.getElementById('module-test').innerHTML = '<span class="error">❌ Module file content unexpected: ' + text.substring(0, 100) + '...</span>';
                }
            } catch (error) {
                document.getElementById('module-test').innerHTML = '<span class="error">❌ Module loading failed: ' + error.message + '</span>';
            }
        }
        
        testModule();
    </script>
</body>
</html>
