// tests/playwright/ux-ui-accessibility.spec.ts
import { test, expect } from '@playwright/test';

// Accessibility and UX checks for main app

test.describe('TargetWise UX/UI & Accessibility', () => {
  test('Main page is keyboard navigable and has ARIA roles', async ({ page }) => {
    await page.goto('/');
    // Tab to the first input
    await page.keyboard.press('Tab');
    // Check that focus is on the file input or textarea
    const active = await page.evaluate(() => document.activeElement?.id);
    expect(['file', 'seed_interests']).toContain(active);
    // Check ARIA roles
    await expect(page.getByRole('form')).toBeVisible();
    await expect(page.getByRole('progressbar')).toBeVisible();
  });

  test('Admin dashboard has accessible status indicators', async ({ page }) => {
    await page.goto('/admin');
    // Status indicator should have a label
    await expect(page.getByText(/Status:/i)).toBeVisible();
    // Check for ARIA attributes on status indicator
    const status = await page.locator('.status-indicator').getAttribute('aria-label');
    expect(status).not.toBeNull();
  });
});
