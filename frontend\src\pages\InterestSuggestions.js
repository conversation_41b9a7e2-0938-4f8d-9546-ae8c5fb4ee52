/**
 * Interest Suggestions Page - Vanilla JavaScript Implementation
 * Provides interest suggestions based on seed interests
 */

export function InterestSuggestionsPage() {
    return `
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="/" class="logo">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <circle cx="12" cy="12" r="6"></circle>
                        <circle cx="12" cy="12" r="2"></circle>
                    </svg>
                    TargetWise
                </a>

                <nav class="nav-menu">
                    <a href="/" class="nav-link">Home</a>
                    <a href="/dashboard" class="nav-link">Micro-Tools</a>
                    <a href="/pricing" class="nav-link">Pricing</a>
                    <a href="/docs" class="nav-link">Docs</a>
                </nav>

                <div class="header-actions">
                    <a href="/admin" class="btn btn-outline">Admin</a>
                    <a href="/" class="btn btn-primary">Create Sheet</a>
                    <button class="theme-toggle">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"></circle>
                            <line x1="12" y1="1" x2="12" y2="3"></line>
                            <line x1="12" y1="21" x2="12" y2="23"></line>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                            <line x1="1" y1="12" x2="3" y2="12"></line>
                            <line x1="21" y1="12" x2="23" y2="12"></line>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Container -->
        <div class="main-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-title">Micro-Tools</div>
                <nav class="sidebar-menu">
                    <a href="/search" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="M21 21l-4.35-4.35"></path>
                        </svg>
                        Interest Search
                    </a>

                    <a href="/suggestions" class="sidebar-item active">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11l3 3L22 4"></path>
                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                        </svg>
                        Interest Suggestions
                    </a>

                    <a href="/taxonomy" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                        </svg>
                        Taxonomy Browser
                    </a>

                    <a href="/pool" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                        Interest Pool
                    </a>
                </nav>
            </aside>

            <!-- Content -->
            <main class="content">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="page-header-top">
                        <h1 class="page-title">
                            <div class="page-icon">💡</div>
                            Interest Suggestions
                        </h1>
                        <div class="page-actions">
                            <button class="btn btn-secondary" id="bulk-suggestions-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                </svg>
                                Bulk Suggestions
                            </button>
                            <button class="btn staging-cart" id="staging-cart-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="9" cy="21" r="1"></circle>
                                    <circle cx="20" cy="21" r="1"></circle>
                                    <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                                </svg>
                                Staging Cart
                                <span class="cart-badge" id="cart-badge">0</span>
                            </button>
                        </div>
                    </div>
                    <p class="page-subtitle">Get AI-powered interest suggestions based on your seed interests to expand your targeting reach</p>
                </div>

                <!-- Content Grid -->
                <div class="content-grid">
                    <!-- Seed Library -->
                    <div class="seed-library">
                        <div class="panel-header">
                            <div class="panel-icon">🌱</div>
                            <h2 class="panel-title">Seed Library</h2>
                        </div>

                        <div class="search-box">
                            <div class="search-input-wrapper">
                                <svg class="search-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.35-4.35"></path>
                                </svg>
                                <input type="text" class="search-input" id="seed-search" placeholder="Search for a seed interest">
                            </div>
                        </div>

                        <div class="seed-list" id="seed-list">
                            <!-- Seed items will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Suggestions Panel -->
                    <div class="suggestions-panel">
                        <div class="panel-header">
                            <div class="panel-icon">🎯</div>
                            <h2 class="panel-title">Get Suggestions</h2>
                        </div>

                        <div class="tabs">
                            <button class="tab active" id="single-tab">Single Suggestions</button>
                            <button class="tab" id="bulk-tab">Bulk Suggestions</button>
                        </div>

                        <form class="suggestions-form" id="suggestions-form">
                            <div class="form-group">
                                <label class="form-label">Select a seed interest</label>
                                <div class="select-wrapper">
                                    <select class="form-select" id="seed-select">
                                        <option value="">-- Select a seed interest --</option>
                                    </select>
                                    <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-get-suggestions">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                                </svg>
                                Get Suggestions
                            </button>
                        </form>

                        <div class="suggestions-section">
                            <div class="bulk-actions" id="bulk-actions" style="display: none;">
                                <span class="bulk-info" id="bulk-info">0 items selected</span>
                                <div class="bulk-buttons">
                                    <button class="btn btn-secondary" id="deselect-all-btn">Deselect All</button>
                                    <button class="btn btn-primary" id="add-selected-btn">Add to Cart</button>
                                </div>
                            </div>

                            <div class="suggestions-header">
                                <h3 class="suggestions-title">Suggestions</h3>
                                <span class="suggestions-count" id="suggestions-count">0 results</span>
                            </div>

                            <div class="loading" id="loading-spinner" style="display: none;">
                                <div class="spinner"></div>
                            </div>

                            <div class="suggestion-list" id="suggestion-list">
                                <!-- Suggestions will be populated by JavaScript -->
                            </div>

                            <div class="empty-state" id="empty-state">
                                <div class="empty-icon">💡</div>
                                <h4 class="empty-title">No suggestions yet</h4>
                                <p class="empty-text">Select a seed interest and click "Get Suggestions" to see related interests</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    `;
}

export function initInterestSuggestionsPage() {
    // Sample data
    const seedLibrary = [
        { id: 'movies', name: 'Movies', category: 'Entertainment', icon: '🎬', audience: '850M' },
        { id: 'technology', name: 'Technology', category: 'Interests', icon: '📱', audience: '720M' },
        { id: 'fitness', name: 'Fitness', category: 'Health & Wellness', icon: '🏃', audience: '540M' },
        { id: 'cooking', name: 'Cooking', category: 'Food & Drink', icon: '🍳', audience: '430M' },
        { id: 'travel', name: 'Travel', category: 'Lifestyle', icon: '✈️', audience: '380M' }
    ];

    const mockSuggestions = [
        { id: 1, name: 'Netflix', audience: '420M', category: 'Streaming' },
        { id: 2, name: 'HBO', audience: '180M', category: 'Entertainment' },
        { id: 3, name: 'Film Festival', audience: '125M', category: 'Events' },
        { id: 4, name: 'IMAX', audience: '95M', category: 'Cinema' },
        { id: 5, name: 'Popcorn', audience: '340M', category: 'Food' },
        { id: 6, name: 'Red Carpet', audience: '78M', category: 'Entertainment' },
        { id: 7, name: 'Action Movies', audience: '560M', category: 'Genre' },
        { id: 8, name: 'Documentary', audience: '290M', category: 'Genre' }
    ];

    // State variables
    let activeTab = 'single';
    let selectedSeed = '';
    let suggestions = [];
    let loading = false;
    let selectedSuggestions = new Set();
    let cartCount = 0;
    let searchTerm = '';
    let selectedSeedItem = null;

    // DOM elements
    const seedSearch = document.getElementById('seed-search');
    const seedList = document.getElementById('seed-list');
    const seedSelect = document.getElementById('seed-select');
    const suggestionsForm = document.getElementById('suggestions-form');
    const singleTab = document.getElementById('single-tab');
    const bulkTab = document.getElementById('bulk-tab');
    const loadingSpinner = document.getElementById('loading-spinner');
    const suggestionListEl = document.getElementById('suggestion-list');
    const emptyState = document.getElementById('empty-state');
    const suggestionsCount = document.getElementById('suggestions-count');
    const bulkActions = document.getElementById('bulk-actions');
    const bulkInfo = document.getElementById('bulk-info');
    const deselectAllBtn = document.getElementById('deselect-all-btn');
    const addSelectedBtn = document.getElementById('add-selected-btn');
    const cartBadge = document.getElementById('cart-badge');
    const stagingCartBtn = document.getElementById('staging-cart-btn');

    // Initialize the page
    function init() {
        renderSeedLibrary();
        populateSeedSelect();
        setupEventListeners();
        updateUI();
    }

    // Render seed library
    function renderSeedLibrary() {
        if (!seedList) return;

        const filteredSeeds = seedLibrary.filter(seed =>
            seed.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            seed.category.toLowerCase().includes(searchTerm.toLowerCase())
        );

        if (filteredSeeds.length > 0) {
            seedList.innerHTML = filteredSeeds.map(seed => `
                <div class="seed-item ${selectedSeedItem === seed.id ? 'selected' : ''}" data-id="${seed.id}">
                    <div class="seed-info">
                        <div class="seed-icon">${seed.icon}</div>
                        <div>
                            <div class="seed-name">${seed.name}</div>
                            <div class="seed-category">${seed.category}</div>
                        </div>
                    </div>
                    <span class="seed-count">${seed.audience}</span>
                </div>
            `).join('');
        } else {
            seedList.innerHTML = '<div class="no-seeds">No matching seeds found</div>';
        }
    }

    // Populate seed select dropdown
    function populateSeedSelect() {
        if (!seedSelect) return;

        seedSelect.innerHTML = '<option value="">-- Select a seed interest --</option>' +
            seedLibrary.map(seed =>
                `<option value="${seed.id}">${seed.name} (${seed.category})</option>`
            ).join('');
    }

    // Setup event listeners
    function setupEventListeners() {
        // Search functionality
        if (seedSearch) {
            seedSearch.addEventListener('input', (e) => {
                searchTerm = e.target.value;
                renderSeedLibrary();
            });
        }

        // Seed item clicks
        if (seedList) {
            seedList.addEventListener('click', (e) => {
                const seedItem = e.target.closest('.seed-item');
                if (seedItem) {
                    const seedId = seedItem.dataset.id;
                    handleSeedItemClick(seedId);
                }
            });
        }

        // Tab switching
        if (singleTab) {
            singleTab.addEventListener('click', () => setActiveTab('single'));
        }
        if (bulkTab) {
            bulkTab.addEventListener('click', () => setActiveTab('bulk'));
        }

        // Form submission
        if (suggestionsForm) {
            suggestionsForm.addEventListener('submit', handleGetSuggestions);
        }

        // Seed select change
        if (seedSelect) {
            seedSelect.addEventListener('change', (e) => {
                selectedSeed = e.target.value;
            });
        }

        // Bulk actions
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', handleDeselectAll);
        }
        if (addSelectedBtn) {
            addSelectedBtn.addEventListener('click', handleAddSelectedToCart);
        }

        // Suggestion interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.suggestion-item')) {
                const suggestionItem = e.target.closest('.suggestion-item');
                const suggestionId = parseInt(suggestionItem.dataset.id);

                if (e.target.closest('.add-btn')) {
                    e.stopPropagation();
                    handleAddToCart(suggestionId);
                } else if (!e.target.closest('.action-btn')) {
                    handleSuggestionSelect(suggestionId);
                }
            }
        });
    }

    // Handle seed item click
    function handleSeedItemClick(seedId) {
        selectedSeedItem = seedId;
        selectedSeed = seedId;
        if (seedSelect) {
            seedSelect.value = seedId;
        }
        renderSeedLibrary();
    }

    // Set active tab
    function setActiveTab(tab) {
        activeTab = tab;
        if (singleTab) {
            singleTab.classList.toggle('active', tab === 'single');
        }
        if (bulkTab) {
            bulkTab.classList.toggle('active', tab === 'bulk');
        }
    }

    // Handle get suggestions
    async function handleGetSuggestions(e) {
        e.preventDefault();

        if (!selectedSeed) {
            window.utils?.showToast('Please select a seed interest', 'warning');
            return;
        }

        loading = true;
        suggestions = [];
        updateUI();

        // Simulate API call
        setTimeout(() => {
            suggestions = [...mockSuggestions];
            loading = false;
            updateUI();
        }, 1500);
    }

    // Handle suggestion selection
    function handleSuggestionSelect(suggestionId) {
        if (selectedSuggestions.has(suggestionId)) {
            selectedSuggestions.delete(suggestionId);
        } else {
            selectedSuggestions.add(suggestionId);
        }
        updateUI();
    }

    // Handle add to cart
    function handleAddToCart(suggestionId) {
        cartCount++;
        updateCartUI();
        window.utils?.showToast('Added to cart', 'success');
    }

    // Handle deselect all
    function handleDeselectAll() {
        selectedSuggestions.clear();
        updateUI();
    }

    // Handle add selected to cart
    function handleAddSelectedToCart() {
        cartCount += selectedSuggestions.size;
        selectedSuggestions.clear();
        updateCartUI();
        updateUI();
        window.utils?.showToast('Selected items added to cart', 'success');
    }

    // Update UI
    function updateUI() {
        updateSuggestionsUI();
        updateBulkActionsUI();
        updateCartUI();
    }

    // Update suggestions UI
    function updateSuggestionsUI() {
        if (suggestionsCount) {
            suggestionsCount.textContent = `${suggestions.length} results`;
        }

        if (loadingSpinner) {
            loadingSpinner.style.display = loading ? 'block' : 'none';
        }

        if (emptyState) {
            emptyState.style.display = !loading && suggestions.length === 0 ? 'block' : 'none';
        }

        if (suggestionListEl) {
            if (!loading && suggestions.length > 0) {
                suggestionListEl.style.display = 'block';
                suggestionListEl.innerHTML = suggestions.map(suggestion => `
                    <div class="suggestion-item ${selectedSuggestions.has(suggestion.id) ? 'selected' : ''}" data-id="${suggestion.id}">
                        <div class="suggestion-content">
                            <div class="suggestion-name">${suggestion.name}</div>
                            <div class="suggestion-meta">
                                <span class="suggestion-stat">
                                    <svg class="suggestion-stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="9" cy="7" r="4"></circle>
                                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                    </svg>
                                    ${suggestion.audience}
                                </span>
                                <span class="suggestion-stat">
                                    <svg class="suggestion-stat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M3 3h18v18H3zM12 8v8m-4-4h8"></path>
                                    </svg>
                                    ${suggestion.category}
                                </span>
                            </div>
                        </div>
                        <div class="suggestion-actions">
                            <button class="action-btn" title="Add to favorites">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                </svg>
                            </button>
                            <button class="action-btn add-btn" title="Add to cart">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
            } else {
                suggestionListEl.style.display = 'none';
            }
        }
    }

    // Update bulk actions UI
    function updateBulkActionsUI() {
        if (bulkActions) {
            bulkActions.style.display = selectedSuggestions.size > 0 ? 'flex' : 'none';
        }
        if (bulkInfo) {
            bulkInfo.textContent = `${selectedSuggestions.size} items selected`;
        }
    }

    // Update cart UI
    function updateCartUI() {
        if (cartBadge) {
            cartBadge.textContent = cartCount;
        }
    }

    // Initialize the page
    init();
}