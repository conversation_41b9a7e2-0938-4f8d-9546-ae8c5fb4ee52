/* TargetWise - Main CSS Entry Point */
/* This file imports all design system components and app-specific styles */

/* Design System Foundation */
@import './design-system/variables.css';
@import './design-system/base.css';
@import './design-system/components.css';
@import './design-system/utilities.css';

/* App-Specific Styles */
@import './targetwise-enhanced.css';
@import './enhanced-pages.css';
@import './enhanced-complete.css'; /* Critical fixes for enhanced design */

/* Layout Components */
.app-header {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.app-header-content {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  text-decoration: none;
  transition: transform var(--transition-base);
}

.app-logo:hover {
  transform: translateY(-1px);
}

.app-logo svg {
  width: 32px;
  height: 32px;
}

.main-layout {
  display: flex;
  max-width: var(--container-max);
  margin: 0 auto;
  gap: var(--space-6);
  padding: var(--space-6);
}

.sidebar {
  width: var(--sidebar-width);
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
  position: sticky;
  top: calc(var(--header-height) + var(--space-6));
}

.sidebar-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--color-gray-400);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-4);
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--color-gray-500);
  font-weight: var(--font-medium);
  transition: all var(--transition-base);
}

.sidebar-item:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-800);
  transform: translateX(4px);
}

.sidebar-item.active {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.main-content {
  flex: 1;
  min-width: 0;
}

/* Page-specific Layouts */
.page-header {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.page-header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-gray-800);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.page-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--text-2xl);
}

.page-subtitle {
  color: var(--color-gray-500);
  font-size: var(--text-base);
  margin-left: 64px;
}

.page-actions {
  display: flex;
  gap: var(--space-3);
}

/* Hero Section for Landing Page */
.hero-section {
  background: var(--gradient-primary);
  padding: var(--space-20) 0;
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: var(--content-max);
  margin: 0 auto;
  padding: 0 var(--space-6);
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: var(--text-5xl);
  font-weight: var(--font-bold);
  color: var(--color-white);
  margin-bottom: var(--space-5);
  line-height: var(--leading-tight);
}

.hero-subtitle {
  font-size: var(--text-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-10);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--leading-normal);
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: var(--space-10);
  flex-wrap: wrap;
}

.hero-feature {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: var(--color-white);
}

.feature-icon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading-content {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  box-shadow: var(--shadow-xl);
}

.loading-text {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-gray-800);
  margin-bottom: var(--space-2);
}

.loading-subtext {
  color: var(--color-gray-500);
}

/* Success Message */
.success-message {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 2px solid var(--color-success);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  animation: slideDown 0.3s ease-out;
}

.success-icon {
  width: 24px;
  height: 24px;
  background: var(--color-success);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  flex-shrink: 0;
}

.success-text {
  color: #059669;
  font-weight: var(--font-medium);
}

/* Animation Keyframes */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .sidebar {
    display: none;
  }
  
  .main-layout {
    padding: var(--space-4);
  }
}

@media (max-width: 640px) {
  .app-header-content {
    padding: var(--space-3) var(--space-4);
  }
  
  .hero-title {
    font-size: var(--text-4xl);
  }
  
  .hero-subtitle {
    font-size: var(--text-lg);
  }
  
  .page-header {
    padding: var(--space-6) var(--space-4);
  }
  
  .page-title {
    font-size: var(--text-2xl);
  }
  
  .main-layout {
    padding: var(--space-4) var(--space-3);
  }
}