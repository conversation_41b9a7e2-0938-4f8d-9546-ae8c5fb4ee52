// DOC: docs/micro-tools/search-bulk-feature.md
const { test, expect } = require('@playwright/test');

test.describe('Dark/Light Mode Transitions and Persistence', () => {
  test('toggle theme and persists across reload', async ({ page }) => {
    await page.goto('/micro-tools/search');
    const initialTheme = await page.evaluate(() => document.documentElement.getAttribute('data-bs-theme'));
    await page.click('#themeToggle');
    const toggledTheme = await page.evaluate(() => document.documentElement.getAttribute('data-bs-theme'));
    expect(toggledTheme).not.toBe(initialTheme);
    await page.reload();
    const reloadedTheme = await page.evaluate(() => document.documentElement.getAttribute('data-bs-theme'));
    expect(reloadedTheme).toBe(toggledTheme);
  });
});