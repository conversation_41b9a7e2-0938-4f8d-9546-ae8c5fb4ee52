# Test Suite

This directory contains the automated tests for TargetWise.

* `frontend/`  – Jest unit tests and Playwright screenshot tests
* `services/`  – Python tests covering the backend service layer

## Jest unit tests

Run all Jest-based unit tests:

```bash
npm test
```

## Playwright screenshot tests

Playwright is used to capture screenshots of the `/micro-tools/search` page and compare them against committed baseline images.

To execute the screenshot tests locally:

```bash
# Install Node dependencies (requires network access)
npm install

# Install Playwright browsers
npx playwright install

# Run the tests
npm run test:e2e
```

The first run will create baseline images in `tests/frontend/playwright/__screenshots__`. Subsequent runs will compare new screenshots against these baselines.

## Service tests

Service tests are written with `pytest` and located in the `tests/services/` directory.

Run them with:

```bash
pytest tests/services
```
