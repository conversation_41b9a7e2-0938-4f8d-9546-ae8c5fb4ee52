# TargetWise Quick Reference

## 🚀 Server Commands

### Start Servers
```bash
# Backend (Terminal 1)
python run.py

# Frontend (Terminal 2)
cd frontend && python server.py
```

### Server URLs
| Service | URL | Purpose |
|---------|-----|---------|
| **Frontend** | http://localhost:8080 | Main web interface |
| **Backend API** | http://localhost:8000 | REST API endpoints |
| **API Docs** | http://localhost:8000/docs | Interactive documentation |

### Stop Servers
- Press `Ctrl+C` in each terminal

## 🔧 Common Commands

### Health Check
```bash
curl http://localhost:8000/health
```

### View Logs
```bash
# Backend logs
tail -f backend.log

# Frontend logs  
tail -f frontend/frontend.log

# Application logs
tail -f logs/app.log
```

### Restart Servers
```bash
# Stop with Ctrl+C, then restart
python run.py                    # Backend
cd frontend && python server.py  # Frontend
```

## 🐛 Troubleshooting

### Port Issues
```bash
# Check what's using port 8000
netstat -ano | findstr :8000  # Windows
lsof -i :8000                 # macOS/Linux

# Use different port
uvicorn api.main:app --port 8001
```

### Dependencies
```bash
# Install missing dependencies
pip install -r requirements/base.txt
pip install pydantic-settings
```

### Reset Application
```bash
# Clear logs
rm -f backend.log frontend/frontend.log logs/app.log

# Restart servers
python run.py
cd frontend && python server.py
```

## 📱 Application Features

### Admin Panel
- URL: http://localhost:8080/admin
- Configure Facebook API credentials
- View system status

### Interest Search
- Single interest search
- Bulk interest search
- Export results to CSV

### Interest Suggestions
- Get related interests
- Bulk suggestions
- Filter by audience size

### Taxonomy Browser
- Browse Facebook interest categories
- Navigate interest hierarchy
- Find specific interest types

## 🔑 Environment Setup

### Required Variables
```env
FACEBOOK_APP_ID=your_app_id
FACEBOOK_ACCESS_TOKEN=your_token
FACEBOOK_AD_ACCOUNT_ID=your_account_id
```

### Optional Variables
```env
DEBUG=True
LOG_LEVEL=INFO
REDIS_URL=redis://localhost:6379/0
```

## 📊 API Endpoints

### Core Endpoints
```bash
# Health check
GET /health

# Interest search
GET /api/targeting/search?q=fitness

# Interest suggestions  
GET /api/targeting/suggestions?interest_id=123

# Taxonomy browse
GET /api/targeting/browse?type=interests
```

### Bulk Operations
```bash
# Bulk search
POST /api/targeting/bulk-search

# Bulk suggestions
POST /api/targeting/bulk-suggestions
```

## 🎯 Quick Start Checklist

- [ ] Python 3.8+ installed
- [ ] Dependencies installed (`pip install -r requirements/base.txt`)
- [ ] Backend started (`python run.py`)
- [ ] Frontend started (`cd frontend && python server.py`)
- [ ] Frontend accessible at http://localhost:8080
- [ ] Backend accessible at http://localhost:8000
- [ ] Facebook API credentials configured
- [ ] Test interest search functionality

## 📞 Support

For detailed instructions: [Server Setup Guide](server-setup.md)
For usage help: [User Guide](user-guide.md)
For API details: http://localhost:8000/docs
