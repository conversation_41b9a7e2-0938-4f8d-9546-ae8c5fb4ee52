<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise - Enhanced Pages Demo</title>
    <link rel="stylesheet" href="/src/styles/index.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 48px;
        }
        
        .demo-title {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .demo-subtitle {
            font-size: 18px;
            color: #64748b;
            margin-bottom: 32px;
        }
        
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 48px;
        }
        
        .page-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s;
            text-decoration: none;
            color: inherit;
        }
        
        .page-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }
        
        .page-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 32px;
        }
        
        .page-card:nth-child(2) .page-icon {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }
        
        .page-card:nth-child(3) .page-icon {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }
        
        .page-card:nth-child(4) .page-icon {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
        }
        
        .page-card:nth-child(5) .page-icon {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }
        
        .page-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .page-features {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #475569;
        }
        
        .feature-check {
            color: #10b981;
        }
        
        .instructions {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 48px;
        }
        
        .instructions-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .instructions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .instructions-list li {
            padding: 8px 0;
            color: #475569;
            display: flex;
            gap: 12px;
        }
        
        .step-number {
            background: #2563eb;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">TargetWise Enhanced Pages</h1>
            <p class="demo-subtitle">Fully functional, interconnected pages with consistent design</p>
        </div>

        <div class="instructions">
            <h2 class="instructions-title">🚀 Getting Started</h2>
            <ul class="instructions-list">
                <li>
                    <span class="step-number">1</span>
                    <span>Click on any page card below to navigate to that page</span>
                </li>
                <li>
                    <span class="step-number">2</span>
                    <span>Use the navigation menu at the top of each page to move between sections</span>
                </li>
                <li>
                    <span class="step-number">3</span>
                    <span>All interactive elements are fully functional - try searching, filtering, and adding items</span>
                </li>
                <li>
                    <span class="step-number">4</span>
                    <span>The design system ensures consistent styling across all pages</span>
                </li>
            </ul>
        </div>

        <div class="pages-grid">
            <a href="/" class="page-card">
                <div class="page-icon">🎯</div>
                <h3 class="page-title">TargetWise Main</h3>
                <p class="page-description">The main landing page with targeting sheet builder functionality</p>
                <div class="page-features">
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>File upload & manual entry</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Country & age targeting</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Sheet generation modal</span>
                    </div>
                </div>
            </a>

            <a href="/dashboard" class="page-card">
                <div class="page-icon">📊</div>
                <h3 class="page-title">Enhanced Dashboard</h3>
                <p class="page-description">Comprehensive analytics and management dashboard</p>
                <div class="page-features">
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Real-time statistics</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Activity tracking</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Sidebar navigation</span>
                    </div>
                </div>
            </a>

            <a href="/search" class="page-card">
                <div class="page-icon">🔍</div>
                <h3 class="page-title">Interest Search</h3>
                <p class="page-description">Advanced search interface with filtering capabilities</p>
                <div class="page-features">
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Advanced filters</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Bulk operations</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Interest details modal</span>
                    </div>
                </div>
            </a>

            <a href="/suggestions" class="page-card">
                <div class="page-icon">💡</div>
                <h3 class="page-title">Interest Suggestions</h3>
                <p class="page-description">AI-powered suggestions based on seed interests</p>
                <div class="page-features">
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Seed interest input</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Customizable results</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Pool integration</span>
                    </div>
                </div>
            </a>

            <a href="/pool" class="page-card">
                <div class="page-icon">🗂️</div>
                <h3 class="page-title">Interest Pool</h3>
                <p class="page-description">Organize and manage saved interests in pools</p>
                <div class="page-features">
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Pool creation</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Category filtering</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-check">✓</span>
                        <span>Pool details view</span>
                    </div>
                </div>
            </a>
        </div>

        <div class="instructions">
            <h2 class="instructions-title">✨ Key Features</h2>
            <ul class="instructions-list">
                <li>
                    <span class="step-number">🎨</span>
                    <span><strong>Design System:</strong> Consistent colors, typography, spacing, and components across all pages</span>
                </li>
                <li>
                    <span class="step-number">🔄</span>
                    <span><strong>Seamless Navigation:</strong> All pages are interconnected with proper routing</span>
                </li>
                <li>
                    <span class="step-number">⚡</span>
                    <span><strong>Full Functionality:</strong> Every button, form, and interactive element works as expected</span>
                </li>
                <li>
                    <span class="step-number">📱</span>
                    <span><strong>Responsive Design:</strong> All pages adapt perfectly to mobile, tablet, and desktop screens</span>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>