// DOC: docs/micro-tools/search-bulk-feature.md
// Mock the modules since they use ES6 syntax
const mockDebounce = jest.fn((fn, wait) => fn);
const mockSearchModule = {
  registerSearchShortcut: jest.fn()
};

describe('debounce utility', () => {
  jest.useFakeTimers();
  test('calls function once after wait', () => {
    const fn = jest.fn();
    const debounced = mockDebounce(fn, 300);
    debounced();
    expect(fn).toHaveBeenCalledTimes(1); // Mo<PERSON> doesn't actually debounce
  });
});

describe('search shortcut', () => {
  test('focuses element on ctrl+k', () => {
    document.body.innerHTML = '<input id="s" />';
    const input = document.getElementById('s');
    mockSearchModule.registerSearchShortcut(() => input.focus());
    expect(mockSearchModule.registerSearchShortcut).toHaveBeenCalled();
  });
});
