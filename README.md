# TargetWise 🎯 - Facebook Algorithmic Targeting 2.0

> Professional fullstack application for building 12-column Algorithmic Targeting sheets using Meta Marketing API

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Python Version](https://img.shields.io/badge/python-3.10%2B-blue.svg)](https://www.python.org/downloads/)
  [![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
  [![Imports: isort](https://img.shields.io/badge/%20imports-isort-%231674b1?style=flat&labelColor=ef8336)](https://pycqa.github.io/isort/)
  [![Type Checker: mypy](https://img.shields.io/badge/type%20checker-mypy-blue.svg)](http://mypy-lang.org/)

  <p align="center">
    <a href="#features">Features</a> •
    <a href="#installation">Installation</a> •
    <a href="#usage">Usage</a> •
    <a href="#documentation">Documentation</a> •
    <a href="#contributing">Contributing</a> •
    <a href="#license">License</a>
  </p>
</div>

## 🚀 Features

- **Algorithmic Targeting 2.0**: Implements the 12-column targeting methodology with 4-4-4 rule
- **Interest Discovery**: Find and analyze Facebook interests with advanced search and suggestions
- **Audience Optimization**: Optimize audience sizes to the 4-5 million "sweet spot"
- **Deduplication**: Ensure no duplicate interests across columns
- **Export**: Generate ready-to-use targeting sheets for Facebook Ads Manager
- **API-First**: Comprehensive RESTful API with OpenAPI documentation
- **Modern Stack**: Built with FastAPI, React, TypeScript, and Tailwind CSS
- **FastAPI Backend**: Modern Python web framework with automatic API documentation
- **Scalable**: Designed for high performance with Redis caching and async processing

## 🛠️ Installation

### Prerequisites

- Python 3.10+
- Node.js 16+ (for frontend development)
- Redis (for caching)
- PostgreSQL (for production, SQLite is used in development)

### Local Development

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/targetwise.git
   cd targetwise
notify.testResults({ passed: 10, failed: 2, skipped: 1, duration: 2500 });
```

For more details, see the [Terminal Notifications documentation](scripts/README.md).

## 📁 Project Structure

```
TargetWise/
├── frontend/          # React/TypeScript frontend
│   ├── src/          # Source code
│   ├── tests/        # Frontend tests
│   └── public/       # Static assets
├── backend/           # FastAPI Python backend
│   ├── app/          # Application code
│   ├── config/       # Configuration
│   └── templates/    # Server templates
├── shared/           # Shared constants and types
├── infrastructure/   # Docker, scripts, CI/CD
└── docs/            # Documentation
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+ (tested with Python 3.12+)
- Node.js 16+ (optional, for development)

### Simple Setup (Recommended)

1. **Clone and setup**
   ```bash
   git clone <repository>
   cd TargetWise
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements/base.txt
   ```

3. **Start backend server**
   ```bash
   python run.py
   ```
   Backend will be available at: http://localhost:8000

4. **Start frontend server** (new terminal)
   ```bash
   cd frontend
   python server.py
   ```
   Frontend will be available at: http://localhost:8080

### 📖 Detailed Setup Guide
For comprehensive setup instructions, troubleshooting, and advanced configuration, see:
**[Server Setup Guide](docs/server-setup.md)**

### Using Docker (Alternative)
   ```bash
   cd infrastructure/docker
   docker-compose up
   ```

## Installation (Legacy)

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/facebook-algorithmic-targeting.git
   cd facebook-algorithmic-targeting
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   # Install Python dependencies (choose one):
   pip install -r requirements/base.txt  # Production dependencies only
   pip install -r requirements/development.txt  # Development dependencies
   pip install pydantic-settings          # Support pydantic v2 BaseSettings

   # Or use modern Python package management:
   pip install -e .  # Install from pyproject.toml

   # Install frontend dependencies:
   npm install
   ```

4. Set up environment variables:
   ```
   cp .env.example .env
   ```
   Edit the `.env` file with your Facebook Ad Account ID and Access Token.

## Usage

1. Start the application:
   ```
   uvicorn app.main:app --reload
   ```

2. Open your browser and navigate to `http://localhost:8000`

3. Upload a CSV file with seed interests or enter them manually

4. Configure targeting parameters (country, age range)

5. Click "Build Targeting Sheet" and wait for the process to complete

6. Download the generated Excel file

## API Endpoints

### Targeting Sheet Operations
- `POST /api/targeting/build`: Build a targeting sheet from seed interests
- `GET /api/targeting/status/{job_id}`: Check the status of a targeting sheet job
- `GET /api/targeting/download/{job_id}`: Download a completed targeting sheet

### Interest Discovery
- `GET /api/targeting/search`: Search for interests by keyword
- `GET /api/targeting/suggestions`: Get suggestions for an interest
- `GET /api/targeting/browse`: Browse interest taxonomy
- `GET /api/targeting/taxonomy`: Get interest taxonomy

### Bulk Operations
- `POST /api/targeting/bulk-search`: Search for multiple interests in parallel
- `POST /api/targeting/bulk-suggestions`: Get suggestions for multiple interests in parallel

### Sample Files
- `GET /api/targeting/sample-csv`: Download sample CSV files for different operations

## Facebook Marketing API Requirements

To use this application, you need:

1. A Facebook Developer account
2. A Facebook App with Marketing API permissions
3. An Ad Account with access to the Marketing API
4. An access token with `ads_management` permission

## Redis Caching

This application uses Redis for persistent caching of API results (interest search, suggestions, reach estimates) to improve performance and reduce redundant external API calls.

- Set the Redis connection string using the `REDIS_URL` environment variable (default: `redis://localhost:6379/0`).
- Interest search and suggestions are cached for 7 days.
- Reach estimates are cached for 24 hours.

Example:
```
export REDIS_URL=redis://localhost:6379/0
```

If Redis is unavailable, the app will fall back to in-memory caching.

Set the `REDIS_URL` environment variable to configure the Redis connection (default: `redis://localhost:6379/0`).


## 📚 Documentation

### Core Documentation
- **[Server Setup Guide](docs/server-setup.md)** - Complete guide to running TargetWise servers
- **[User Guide](docs/user-guide.md)** - How to use the application features
- **[API Documentation](docs/api/endpoints.md)** - REST API reference
- **[Architecture Overview](docs/architecture.md)** - System design and components

### Additional Resources
- **[Frontend Documentation](docs/frontend/README.md)** - Frontend development guide
- **[Maintenance Guide](docs/maintenance/)** - System maintenance and cleanup procedures

## 🧪 Testing

The main project documentation is maintained in [`docs/project-structure.md`](docs/project-structure.md), which outlines the directory layout and links to all sub-sections (API docs, design system, etc.).

```bash
pytest            # run all tests
pytest tests/api  # API tests only
pytest tests/services  # service tests only
pytest tests/integration  # integration tests
```

Frontend tests can be run with `npm`:

```bash
npm test             # Jest unit tests
npm run test:e2e     # Playwright screenshot tests
npm run clean:assets # Remove stale compiled assets (CSS/JS)
npm run build:ts     # Compile TypeScript → static/js
npm run build:css    # Compile SCSS → static/css
npm run build        # Clean + TS + CSS in one go
```

## Terminal Notifications

For comprehensive terminal notifications covering process status, build progress, audit results, test coverage, Git hooks, and resource monitoring, see [scripts/README.md](scripts/README.md).

## Linting & Formatting

Python code is formatted with [Black](https://black.readthedocs.io/en/stable/) and import order is enforced via [isort](https://pycqa.github.io/isort/). A basic [Flake8](https://flake8.pycqa.org/) configuration is also provided. JavaScript/TypeScript code is formatted using [Prettier](https://prettier.io/) and linted with ESLint.

### Pre-commit Hooks

This project uses [pre-commit](https://pre-commit.com/) to enforce code quality standards. To set up pre-commit hooks:

1. Install pre-commit:
   ```bash
   pip install pre-commit
   ```

2. Install the git hooks:
   ```bash
   pre-commit install
   ```

3. The hooks will now run automatically on every commit. You can also run them manually:
   ```bash
   pre-commit run --all-files
   ```

The pre-commit configuration includes:
- Black (Python formatting)
- isort (Python import sorting)
- Flake8 (Python linting)
- Prettier (JavaScript/TypeScript/JSON/CSS formatting)
- ESLint (JavaScript/TypeScript linting)
- Various file checks (YAML, JSON, TOML, trailing whitespace, etc.)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
6. Review the [design system guidelines](docs/design-system/README.md) before submitting UI changes
7. Consult the [Unified UI Component Strategy](docs/unified-ui-component-strategy.md) when working on new interface features
8. Refer to the [UI Component Library](/ui-components) for building reusable widgets

## License

This project is licensed under the [MIT License](LICENSE).

## Acknowledgements

- Meta Marketing API documentation
- Algorithmic Targeting 2.0 methodology
- FastAPI framework
- React for frontend components
