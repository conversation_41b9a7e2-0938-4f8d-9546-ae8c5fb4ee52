#!/usr/bin/env python3
"""Test backend imports and startup."""

import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent)
if project_root not in sys.path:
    sys.path.append(project_root)

print("Testing backend imports...")

try:
    print("1. Testing services import...")
    from api.services import (
        FacebookBaseService,
        TaxonomyService,
        InterestSearchService,
        SuggestionService,
        ReachEstimationService,
        TargetingBuilder,
        InterestClassifier,
    )
    print("✓ Services imported successfully")
    
    print("\n2. Testing API routes import...")
    from api.api.v1 import admin_router, api_router
    print("✓ API routes imported successfully")
    
    print("\n3. Testing main app import...")
    from api.main import app
    print("✓ Main app imported successfully")
    
    print("\n✅ All imports successful! Backend should start without errors.")
    print("\nTo run the backend:")
    print("  python run.py")
    
except ImportError as e:
    print(f"\n❌ Import error: {e}")
    print("\nPlease check the following:")
    print("1. Ensure all required packages are installed")
    print("2. Check that all service files exist")
    print("3. Verify import paths are correct")