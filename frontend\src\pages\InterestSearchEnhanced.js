/**
 * Interest Search Enhanced Page - Matches interest-search-enhanced.html
 */

export function InterestSearchEnhancedPage() {
  return `
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <a href="/" class="logo">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            TargetWise
          </a>

          <nav class="nav-menu">
            <a href="/" class="nav-link">Home</a>
            <a href="/dashboard" class="nav-link">Micro-Tools</a>
            <a href="/pricing" class="nav-link">Pricing</a>
            <a href="/docs" class="nav-link">Docs</a>
          </nav>

          <div class="header-actions">
            <a href="/admin" class="btn btn-outline">Admin</a>
            <a href="/" class="btn btn-primary">Create Sheet</a>
            <button class="theme-toggle">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="5"></circle>
                <line x1="12" y1="1" x2="12" y2="3"></line>
                <line x1="12" y1="21" x2="12" y2="23"></line>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                <line x1="1" y1="12" x2="3" y2="12"></line>
                <line x1="21" y1="12" x2="23" y2="12"></line>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
              </svg>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Container -->
      <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
          <div class="sidebar-title">Micro-Tools</div>
          <nav class="sidebar-menu">
            <a href="/search" class="sidebar-item active">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
              Interest Search
            </a>

            <a href="/suggestions" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 11l3 3L22 4"></path>
                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
              </svg>
              Interest Suggestions
            </a>

            <a href="/taxonomy" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
              Taxonomy Browser
            </a>

            <a href="/pool" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              Interest Pool
            </a>
          </nav>
        </aside>

        <!-- Content -->
        <div class="content">
          <!-- Page Header -->
          <div class="page-header">
            <div class="page-header-top">
              <h1 class="page-title">
                <div class="page-icon">🔍</div>
                Interest Search
              </h1>
              <div class="page-actions">
                <button class="btn btn-secondary" id="bulk-search-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                  Bulk Search
                </button>
                <button class="btn btn-primary" id="view-pool-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                  View Pool
                </button>
              </div>
            </div>
            <p class="page-subtitle">Search for specific interests using keywords and get detailed audience data</p>
          </div>

          <!-- Search Panel -->
          <div class="search-panel">
            <div class="search-header">
              <h2 class="search-title">Search Interests</h2>
              <div class="tab-container">
                <button class="tab active" data-tab="single">Single Search</button>
                <button class="tab" data-tab="bulk">Bulk Search</button>
              </div>
            </div>

            <form class="search-form" id="search-form">
              <div class="form-group">
                <label class="form-label" for="search-input">Search Query</label>
                <div class="search-input-wrapper">
                  <svg class="search-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="M21 21l-4.35-4.35"></path>
                  </svg>
                  <input
                    type="text"
                    id="search-input"
                    class="search-input"
                    placeholder="Enter keywords (e.g., fitness, technology, fashion)"
                    autofocus
                  >
                </div>
                <div class="input-hint">Use specific keywords for better results</div>
              </div>

              <div class="form-group">
                <label class="form-label" for="location-select">Location</label>
                <div class="select-wrapper">
                  <select id="location-select" class="form-select">
                    <option value="US">United States</option>
                    <option value="GB">United Kingdom</option>
                    <option value="CA">Canada</option>
                    <option value="AU">Australia</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                  </select>
                  <svg class="select-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </div>
              </div>

              <div class="search-actions">
                <button type="submit" class="btn btn-primary btn-search">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="M21 21l-4.35-4.35"></path>
                  </svg>
                  Search Interests
                </button>
                <button type="button" class="btn btn-save" id="save-search">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                  </svg>
                  Save Search
                </button>
              </div>
            </form>
          </div>

          <!-- Results Section -->
          <div class="results-section" id="results-section" style="display: none;">
            <div class="results-header">
              <h2 class="results-title">
                Search Results
                <span class="results-badge" id="result-count">0</span>
              </h2>
              <div class="results-actions">
                <button class="btn btn-icon" id="export-results" title="Export Results">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                </button>
                <button class="btn btn-icon" id="filter-results" title="Filter Results">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                  </svg>
                </button>
                <button class="btn btn-primary" id="add-selected" disabled>
                  Add Selected (<span id="selected-count">0</span>)
                </button>
              </div>
            </div>

            <div class="results-table">
              <table>
                <thead>
                  <tr>
                    <th>
                      <input type="checkbox" id="select-all-checkbox">
                    </th>
                    <th>Interest Name</th>
                    <th>Audience Size</th>
                    <th>Type</th>
                    <th>Path</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="results-tbody">
                  <!-- Results will be populated here -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Loading State -->
          <div class="loading-overlay" id="loading-state" style="display: none;">
            <div class="loading-content">
              <div class="spinner"></div>
              <p class="loading-text">Searching for interests...</p>
              <p class="loading-subtext">This may take a few seconds</p>
            </div>
          </div>

          <!-- Empty State -->
          <div class="empty-state" id="empty-state">
            <div class="empty-icon">🔍</div>
            <h3 class="empty-title">Start searching for interests</h3>
            <p class="empty-subtitle">Enter keywords above to find relevant Facebook interests for your campaigns</p>
          </div>
        </div>
      </div>

  `;
}

export function initInterestSearchEnhancedPage() {
  const searchForm = document.getElementById('search-form');
  const searchInput = document.getElementById('search-input');
  const selectedInterests = new Set();

  // Handle sidebar navigation
  const sidebarItems = document.querySelectorAll('.sidebar-item');
  sidebarItems.forEach(item => {
    item.addEventListener('click', (e) => {
      sidebarItems.forEach(i => i.classList.remove('active'));
      item.classList.add('active');
    });
  });

  // Handle tab switching
  const tabs = document.querySelectorAll('.tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      const tabType = tab.dataset.tab;
      if (tabType === 'bulk') {
        // Switch to bulk search mode
        searchInput.placeholder = 'Enter multiple keywords separated by commas';
      } else {
        // Switch to single search mode
        searchInput.placeholder = 'Enter keywords (e.g., fitness, technology, fashion)';
      }
    });
  });

  // Search functionality
  async function performSearch(e) {
    e.preventDefault();
    const query = searchInput.value.trim();
    if (!query) return;

    // Show loading state
    document.getElementById('loading-state').style.display = 'flex';
    document.getElementById('empty-state').style.display = 'none';
    document.getElementById('results-section').style.display = 'none';

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock results
    const results = generateMockResults(query);

    // Hide loading
    document.getElementById('loading-state').style.display = 'none';

    if (results.length === 0) {
      document.getElementById('empty-state').style.display = 'block';
    } else {
      displayResults(results);
    }
  }

  // Display search results
  function displayResults(results) {
    const resultsSection = document.getElementById('results-section');
    const resultsTbody = document.getElementById('results-tbody');
    const resultCount = document.getElementById('result-count');

    resultCount.textContent = results.length;
    resultsSection.style.display = 'block';

    resultsTbody.innerHTML = results.map(result => `
      <tr data-id="${result.id}">
        <td>
          <input type="checkbox" class="interest-checkbox" id="interest-${result.id}">
        </td>
        <td>
          <div class="interest-name-cell">
            <strong>${result.name}</strong>
            <button class="copy-btn" data-text="${result.name}" title="Copy to clipboard">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
            </button>
          </div>
        </td>
        <td>${result.audience}</td>
        <td>${result.type}</td>
        <td>${result.path}</td>
        <td>
          <div class="table-actions">
            <button class="btn btn-icon quick-add" data-id="${result.id}" title="Add to Pool">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
            <button class="btn btn-icon view-suggestions" data-id="${result.id}" title="Get Suggestions">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 11l3 3L22 4"></path>
                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
              </svg>
            </button>
          </div>
        </td>
      </tr>
    `).join('');

    // Add event handlers
    updateResultHandlers();
  }

  // Generate mock results
  function generateMockResults(query) {
    const mockData = [
      {
        id: 1,
        name: `${query} Enthusiasts`,
        audience: '2.4M',
        type: 'Interest',
        path: `Interests > ${query}`,
        description: `People interested in ${query} and related topics`
      },
      {
        id: 2,
        name: `${query} Products`,
        audience: '1.8M',
        type: 'Interest',
        path: `Interests > Shopping > ${query}`,
        description: `Consumers interested in ${query} products and brands`
      },
      {
        id: 3,
        name: `Online ${query}`,
        audience: '3.2M',
        type: 'Interest',
        path: `Interests > Technology > ${query}`,
        description: `Digital and online ${query} activities`
      },
      {
        id: 4,
        name: `${query} Community`,
        audience: '890K',
        type: 'Interest',
        path: `Interests > Community > ${query}`,
        description: `Active members of ${query} communities`
      },
      {
        id: 5,
        name: `${query} Trends`,
        audience: '1.5M',
        type: 'Interest',
        path: `Interests > Lifestyle > ${query}`,
        description: `People following ${query} trends and news`
      }
    ];

    return mockData;
  }

  // Update result handlers
  function updateResultHandlers() {
    // Checkbox selection
    const checkboxes = document.querySelectorAll('.interest-checkbox');
    const selectedCount = document.getElementById('selected-count');
    const addSelectedBtn = document.getElementById('add-selected');

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const id = checkbox.id.replace('interest-', '');
        if (checkbox.checked) {
          selectedInterests.add(id);
        } else {
          selectedInterests.delete(id);
        }

        selectedCount.textContent = selectedInterests.size;
        addSelectedBtn.disabled = selectedInterests.size === 0;
      });
    });

    // Select all functionality
    document.getElementById('select-all').addEventListener('click', () => {
      const allChecked = Array.from(checkboxes).every(cb => cb.checked);
      checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        const id = cb.id.replace('interest-', '');
        if (cb.checked) {
          selectedInterests.add(id);
        } else {
          selectedInterests.delete(id);
        }
      });

      selectedCount.textContent = selectedInterests.size;
      addSelectedBtn.disabled = selectedInterests.size === 0;
    });

    // Interest info buttons
    document.querySelectorAll('.interest-info').forEach(btn => {
      btn.addEventListener('click', () => {
        showInterestDetails(btn.dataset.id);
      });
    });

    // Copy button handlers
    document.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const text = btn.dataset.text;
        navigator.clipboard.writeText(text).then(() => {
          // Show success feedback
          const originalColor = btn.style.color;
          btn.style.color = '#10b981';
          setTimeout(() => {
            btn.style.color = originalColor;
          }, 1000);
          showNotification('Copied to clipboard!', 'success');
        });
      });
    });

    // Quick add buttons
    document.querySelectorAll('.quick-add').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const id = btn.closest('[data-id]').dataset.id;
        console.log('Quick add interest:', id);
        showNotification('Interest added to pool!', 'success');
      });
    });

    // View suggestions buttons
    document.querySelectorAll('.view-suggestions').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const id = btn.closest('[data-id]').dataset.id;
        window.location.href = `/suggestions?interest=${id}`;
      });
    });
  }

  // Show interest details modal
  function showInterestDetails(interestId) {
    const modal = document.getElementById('interest-details-modal');

    // Populate modal with mock data
    document.getElementById('interest-name').textContent = 'Fashion Enthusiasts';
    document.getElementById('interest-id').textContent = interestId;
    document.getElementById('interest-category').textContent = 'Interests';
    document.getElementById('interest-audience').textContent = '2.4M - 2.8M';
    document.getElementById('interest-description').textContent = 'People who have expressed an interest in or like pages related to Fashion';

    // Add related topics
    const relatedTopics = ['Style', 'Clothing', 'Fashion Brands', 'Shopping'];
    document.getElementById('related-topics').innerHTML = relatedTopics
      .map(topic => `<span class="tag">${topic}</span>`)
      .join('');

    modal.style.display = 'flex';
  }

  // Event handlers
  searchInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
      performSearch();
    }
  });

  searchBtn.addEventListener('click', performSearch);

  // Filters toggle
  filtersToggle.addEventListener('click', () => {
    const isVisible = advancedFilters.style.display !== 'none';
    advancedFilters.style.display = isVisible ? 'none' : 'block';
    filtersToggle.classList.toggle('active');
  });

  // Reset filters
  document.getElementById('reset-filters').addEventListener('click', () => {
    document.getElementById('category-filter').value = '';
    document.getElementById('audience-size-filter').value = '';
    document.getElementById('country-filter').value = '';
    document.getElementById('sort-filter').value = 'relevance';
  });

  // Apply filters
  document.getElementById('apply-filters').addEventListener('click', () => {
    performSearch();
  });

  // Modal handlers
  const modal = document.getElementById('interest-details-modal');
  const modalClose = document.querySelector('.modal-close');
  const closeDetailsBtn = document.getElementById('close-details');

  [modalClose, closeDetailsBtn].forEach(element => {
    element.addEventListener('click', () => {
      modal.style.display = 'none';
    });
  });

  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.style.display = 'none';
    }
  });

  // Bulk search button
  document.getElementById('bulk-search-btn').addEventListener('click', () => {
    console.log('Opening bulk search');
  });

  // View pool button
  document.getElementById('view-pool-btn').addEventListener('click', () => {
    window.location.href = '/pool';
  });
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;

  document.body.appendChild(notification);

  setTimeout(() => notification.classList.add('show'), 10);

  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => notification.remove(), 300);
  }, 3000);
}