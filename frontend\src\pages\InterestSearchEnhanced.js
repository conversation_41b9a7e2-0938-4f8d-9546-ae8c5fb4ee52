/**
 * Interest Search Enhanced Page - Matches interest-search-enhanced.html
 */

export function InterestSearchEnhancedPage() {
  return `
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <a href="/" class="logo">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            TargetWise
          </a>

          <nav class="nav-menu">
            <a href="/" class="nav-link">Home</a>
            <a href="/dashboard" class="nav-link active">Micro-Tools</a>
            <a href="/docs" class="nav-link">Documentation</a>
            <a href="/pricing" class="nav-link">Pricing</a>
            <a href="/admin" class="nav-link">Admin</a>
          </nav>

          <div class="header-actions">
            <button class="theme-toggle">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="5"></circle>
                <line x1="12" y1="1" x2="12" y2="3"></line>
                <line x1="12" y1="21" x2="12" y2="23"></line>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                <line x1="1" y1="12" x2="3" y2="12"></line>
                <line x1="21" y1="12" x2="23" y2="12"></line>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
              </svg>
            </button>
            <a href="/login" class="btn btn-outline">Log In</a>
            <a href="/signup" class="btn btn-primary">Sign Up Free</a>
          </div>
        </div>
      </header>

      <!-- Main Container -->
      <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
          <h3 class="sidebar-title">Micro-Tools</h3>
          <nav class="sidebar-menu">
            <a href="/dashboard" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg>
              Dashboard
            </a>
            <a href="/search" class="sidebar-item active">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              Interest Search
            </a>
            <a href="/suggestions" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
              </svg>
              Interest Suggestions
            </a>
            <a href="/taxonomy" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3v18h18"></path>
                <path d="m19 9-5 5-4-4-3 3"></path>
              </svg>
              Taxonomy Browser
            </a>
            <a href="/pool" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
              </svg>
              Interest Pool
            </a>
          </nav>
        </aside>

        <!-- Content -->
        <div class="content">
          <!-- Page Header -->
          <div class="page-header">
            <div class="page-header-top">
              <div>
                <h1 class="page-title">
                  <div class="page-icon">🔍</div>
                  Interest Search
                </h1>
                <p class="page-subtitle">Search for Facebook interests and add them to your pool</p>
              </div>
              <div class="page-actions">
                <button class="btn btn-secondary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  Back to Tools
                </button>
                <button class="btn btn-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                    <line x1="4" y1="22" x2="4" y2="15"></line>
                  </svg>
                  Main App
                </button>
              </div>
            </div>
          </div>

          <!-- Search Panel -->
          <div class="search-panel">
            <div class="search-header">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <h2 class="search-title">Find Interests</h2>
              <div class="tab-container">
                <button class="tab active" id="singleTab">Single Search</button>
                <button class="tab" id="bulkTab">Bulk Search</button>
              </div>
            </div>

            <form class="search-form">
              <div class="form-group">
                <label class="form-label">Search Keywords</label>
                <div class="search-input-wrapper">
                  <svg class="search-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                  </svg>
                  <input type="text" class="search-input" placeholder="Enter a keyword like 'hiking', 'movies', or 'technology'">
                </div>
                <p class="input-hint">Tip: Use specific keywords to find targeted audience interests</p>
              </div>

              <div class="form-group">
                <label class="form-label">Search Type</label>
                <div class="select-wrapper">
                  <select class="form-select">
                    <option>Interests</option>
                    <option>Behaviors</option>
                    <option>Demographics</option>
                    <option>Job Titles</option>
                    <option>Employers</option>
                  </select>
                  <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </div>
              </div>

              <div class="search-actions">
                <button type="submit" class="btn btn-primary btn-search">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                  </svg>
                  Search
                </button>
                <button type="button" class="btn btn-save">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                  </svg>
                  Save Search
                </button>
              </div>
            </form>
          </div>

          <!-- Results Section -->
          <div class="results-section">
            <div class="results-header">
              <h3 class="results-title">
                Results
                <span class="results-badge">0</span>
              </h3>
              <div class="results-actions">
                <button class="btn btn-icon btn-secondary" title="Select All">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <polyline points="9 11 12 14 22 4"></polyline>
                  </svg>
                </button>
                <button class="btn btn-icon btn-secondary" title="Export Selected">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                </button>
                <button class="btn btn-icon btn-secondary" title="Export All">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                </button>
                <button class="btn btn-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5Z"></path>
                    <path d="M12 5L8 21l4-7 4 7-4-16"></path>
                  </svg>
                  Cart <span style="background: white; color: #2563eb; padding: 2px 8px; border-radius: 12px; margin-left: 4px;">2</span>
                </button>
              </div>
            </div>

            <div class="results-table">
              <!-- Empty State -->
              <div class="empty-state">
                <div class="empty-icon">🔍</div>
                <h4 class="empty-title">No results yet</h4>
                <p class="empty-text">Start searching to find Facebook interests for your campaigns</p>
              </div>

              <!-- Results Table (hidden by default) -->
              <table style="display: none;">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th style="width: 50px;">Favorite</th>
                    <th>Name</th>
                    <th>ID</th>
                    <th>Audience Size</th>
                    <th>Type</th>
                    <th>Path</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody id="resultsBody">
                  <!-- Results will be populated here -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Search History -->
          <div class="history-section">
            <div class="history-header">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <h3 class="history-title">Search History</h3>
            </div>
            <div class="history-list">
              <div class="history-item">
                <span class="history-term">cinema</span>
                <span class="history-time">2 minutes ago</span>
              </div>
              <div class="history-item">
                <span class="history-term">projector</span>
                <span class="history-time">5 minutes ago</span>
              </div>
              <div class="history-item">
                <span class="history-term">projector</span>
                <span class="history-time">8 minutes ago</span>
              </div>
              <div class="history-item">
                <span class="history-term">'projector', 'cinema'</span>
                <span class="history-time">12 minutes ago</span>
              </div>
            </div>
          </div>
        </main>
      </div>

  `;
}

export function initInterestSearchEnhancedPage() {
  const searchForm = document.querySelector('.search-form');
  const searchInput = document.querySelector('.search-input');
  const selectedInterests = new Set();

  // Check if elements exist before adding event listeners
  if (!searchInput) {
    console.warn('Search input not found');
    return;
  }

  // Handle sidebar navigation
  const sidebarItems = document.querySelectorAll('.sidebar-item');
  sidebarItems.forEach(item => {
    item.addEventListener('click', (e) => {
      sidebarItems.forEach(i => i.classList.remove('active'));
      item.classList.add('active');
    });
  });

  // Handle tab switching
  const tabs = document.querySelectorAll('.tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      const tabType = tab.dataset.tab;
      if (tabType === 'bulk') {
        // Switch to bulk search mode
        searchInput.placeholder = 'Enter multiple keywords separated by commas';
      } else {
        // Switch to single search mode
        searchInput.placeholder = 'Enter keywords (e.g., fitness, technology, fashion)';
      }
    });
  });

  // Search functionality
  async function performSearch(e) {
    e.preventDefault();
    const query = searchInput.value.trim();
    if (!query) return;

    // Show loading state by hiding empty state and showing table
    const emptyState = document.querySelector('.empty-state');
    const resultsTable = document.querySelector('.results-table table');
    const resultsBadge = document.querySelector('.results-badge');

    if (emptyState) emptyState.style.display = 'none';

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock results
    const results = generateMockResults(query);

    if (results.length === 0) {
      if (emptyState) emptyState.style.display = 'block';
      if (resultsTable) resultsTable.style.display = 'none';
    } else {
      displayResults(results);
    }
  }

  // Display search results
  function displayResults(results) {
    const emptyState = document.querySelector('.empty-state');
    const resultsTable = document.querySelector('.results-table table');
    const resultsTbody = document.getElementById('resultsBody');
    const resultsBadge = document.querySelector('.results-badge');

    // Update results count
    if (resultsBadge) {
      resultsBadge.textContent = results.length;
    }

    // Hide empty state and show table
    if (emptyState) emptyState.style.display = 'none';
    if (resultsTable) resultsTable.style.display = 'table';

    // Populate results
    if (resultsTbody) {
      resultsTbody.innerHTML = results.map(result => `
        <tr data-id="${result.id}">
          <td>
            <input type="checkbox" class="interest-checkbox" id="interest-${result.id}">
          </td>
          <td>
            <button class="btn btn-icon" title="Add to favorites">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5Z"></path>
              </svg>
            </button>
          </td>
          <td>
            <div class="interest-name-cell">
              <strong>${result.name}</strong>
              <button class="copy-btn" data-text="${result.name}" title="Copy to clipboard">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
            </div>
          </td>
          <td>${result.id}</td>
          <td>${result.audience}</td>
          <td>${result.type}</td>
          <td>${result.path}</td>
          <td>
            <button class="btn btn-icon quick-add" data-id="${result.id}" title="Add to Pool">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
          </td>
        </tr>
      `).join('');

      // Add event handlers
      updateResultHandlers();
    }
  }

  // Generate mock results
  function generateMockResults(query) {
    const mockData = [
      {
        id: 1,
        name: `${query} Enthusiasts`,
        audience: '2.4M',
        type: 'Interest',
        path: `Interests > ${query}`,
        description: `People interested in ${query} and related topics`
      },
      {
        id: 2,
        name: `${query} Products`,
        audience: '1.8M',
        type: 'Interest',
        path: `Interests > Shopping > ${query}`,
        description: `Consumers interested in ${query} products and brands`
      },
      {
        id: 3,
        name: `Online ${query}`,
        audience: '3.2M',
        type: 'Interest',
        path: `Interests > Technology > ${query}`,
        description: `Digital and online ${query} activities`
      },
      {
        id: 4,
        name: `${query} Community`,
        audience: '890K',
        type: 'Interest',
        path: `Interests > Community > ${query}`,
        description: `Active members of ${query} communities`
      },
      {
        id: 5,
        name: `${query} Trends`,
        audience: '1.5M',
        type: 'Interest',
        path: `Interests > Lifestyle > ${query}`,
        description: `People following ${query} trends and news`
      }
    ];

    return mockData;
  }

  // Update result handlers
  function updateResultHandlers() {
    // Copy button handlers
    document.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const text = btn.dataset.text;
        navigator.clipboard.writeText(text).then(() => {
          // Show success feedback
          const originalColor = btn.style.color;
          btn.style.color = '#10b981';
          setTimeout(() => {
            btn.style.color = originalColor;
          }, 1000);
          showNotification('Copied to clipboard!', 'success');
        });
      });
    });

    // Quick add buttons
    document.querySelectorAll('.quick-add').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const id = btn.closest('[data-id]').dataset.id;
        console.log('Quick add interest:', id);
        showNotification('Interest added to pool!', 'success');
      });
    });
  }



  // Event handlers
  if (searchInput) {
    searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        performSearch(e);
      }
    });
  }

  const searchBtn = document.querySelector('.btn-search');
  if (searchBtn) {
    searchBtn.addEventListener('click', performSearch);
  }

  // Form submission
  if (searchForm) {
    searchForm.addEventListener('submit', performSearch);
  }

  // Tab switching functionality
  const tabs = document.querySelectorAll('.tab');
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      // Update search input placeholder based on tab
      if (searchInput) {
        const tabId = tab.id;
        if (tabId === 'bulkTab') {
          searchInput.placeholder = 'Enter multiple keywords separated by commas';
        } else {
          searchInput.placeholder = "Enter a keyword like 'hiking', 'movies', or 'technology'";
        }
      }
    });
  });
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;

  document.body.appendChild(notification);

  setTimeout(() => notification.classList.add('show'), 10);

  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => notification.remove(), 300);
  }, 3000);
}