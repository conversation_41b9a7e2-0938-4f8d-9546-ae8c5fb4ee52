/**
 * Interest Search Enhanced Page - Matches interest-search-enhanced.html
 */

export function InterestSearchEnhancedPage() {
  return `
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <a href="/" class="logo">
            <svg viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="14" stroke="currentColor" stroke-width="2" fill="none"/>
              <circle cx="16" cy="16" r="8" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.3"/>
              <circle cx="16" cy="16" r="4" fill="currentColor"/>
            </svg>
            TargetWise
          </a>
          
          <nav class="nav-menu">
            <a href="/" class="nav-link">Home</a>
            <a href="/dashboard" class="nav-link">Dashboard</a>
            <a href="/search" class="nav-link active">Interest Search</a>
            <a href="/suggestions" class="nav-link">Suggestions</a>
            <a href="/pool" class="nav-link">Interest Pool</a>
          </nav>
          
          <div class="header-actions">
            <button class="theme-toggle" aria-label="Toggle theme">
              <svg class="theme-icon-light" width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"/>
              </svg>
            </button>
          </div>
        </div>
      </header>

      <!-- Page Content -->
      <main class="main-content">
        <div class="container">
          <!-- Page Header -->
          <div class="page-header">
            <div>
              <h1 class="page-title">Interest Search</h1>
              <p class="page-subtitle">Find and explore Facebook interests for your targeting</p>
            </div>
            <div class="page-actions">
              <button class="btn-secondary" id="bulk-search-btn">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H4v10h12V5h-1a1 1 0 100-2 2 2 0 012 2v11a2 2 0 01-2 2H5a2 2 0 01-2-2V5z" clip-rule="evenodd"/>
                </svg>
                Bulk Search
              </button>
              <button class="btn-primary" id="view-pool-btn">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v1a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v1a2 2 0 002 2h10a2 2 0 002-2v-1a2 2 0 00-2-2H5z"/>
                </svg>
                View Pool
              </button>
            </div>
          </div>

          <!-- Search Section -->
          <div class="search-card">
            <div class="search-container">
              <div class="search-input-wrapper">
                <svg class="search-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="11" cy="11" r="8"/>
                  <path d="M21 21l-4.35-4.35"/>
                </svg>
                <input 
                  type="text" 
                  id="search-input" 
                  class="search-input" 
                  placeholder="Search for interests (e.g., Fashion, Technology, Sports...)"
                  autofocus
                >
                <button class="search-btn" id="search-btn">
                  Search
                </button>
              </div>
              
              <!-- Advanced Filters Toggle -->
              <button class="advanced-filters-toggle" id="filters-toggle">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"/>
                </svg>
                Advanced Filters
                <svg class="toggle-arrow" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 01.708 0L8 10.293l5.646-5.647a.5.5 0 01.708.708l-6 6a.5.5 0 01-.708 0l-6-6a.5.5 0 010-.708z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
            
            <!-- Advanced Filters -->
            <div class="advanced-filters" id="advanced-filters" style="display: none;">
              <div class="filters-grid">
                <div class="filter-group">
                  <label for="category-filter">Category</label>
                  <select id="category-filter" class="form-select">
                    <option value="">All Categories</option>
                    <option value="business">Business & Industry</option>
                    <option value="entertainment">Entertainment</option>
                    <option value="family">Family & Relationships</option>
                    <option value="fitness">Fitness & Wellness</option>
                    <option value="food">Food & Drink</option>
                    <option value="hobbies">Hobbies & Activities</option>
                    <option value="shopping">Shopping & Fashion</option>
                    <option value="sports">Sports & Outdoors</option>
                    <option value="technology">Technology</option>
                  </select>
                </div>
                
                <div class="filter-group">
                  <label for="audience-size-filter">Audience Size</label>
                  <select id="audience-size-filter" class="form-select">
                    <option value="">Any Size</option>
                    <option value="small">Small (< 1M)</option>
                    <option value="medium">Medium (1M - 10M)</option>
                    <option value="large">Large (10M - 50M)</option>
                    <option value="xlarge">Very Large (> 50M)</option>
                  </select>
                </div>
                
                <div class="filter-group">
                  <label for="country-filter">Country</label>
                  <select id="country-filter" class="form-select">
                    <option value="">All Countries</option>
                    <option value="US">United States</option>
                    <option value="GB">United Kingdom</option>
                    <option value="CA">Canada</option>
                    <option value="AU">Australia</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                  </select>
                </div>
                
                <div class="filter-group">
                  <label for="sort-filter">Sort By</label>
                  <select id="sort-filter" class="form-select">
                    <option value="relevance">Relevance</option>
                    <option value="audience_desc">Audience Size (High to Low)</option>
                    <option value="audience_asc">Audience Size (Low to High)</option>
                    <option value="name">Name (A-Z)</option>
                  </select>
                </div>
              </div>
              
              <div class="filter-actions">
                <button class="btn-text" id="reset-filters">Reset Filters</button>
                <button class="btn-primary" id="apply-filters">Apply Filters</button>
              </div>
            </div>
          </div>

          <!-- Results Stats -->
          <div class="results-stats" id="results-stats" style="display: none;">
            <p class="results-count">
              Found <strong id="result-count">0</strong> interests
            </p>
            <div class="results-actions">
              <button class="btn-text" id="select-all">Select All</button>
              <button class="btn-primary" id="add-selected" disabled>
                Add Selected to Pool
                <span class="badge" id="selected-count">0</span>
              </button>
            </div>
          </div>

          <!-- Results Grid -->
          <div class="results-grid" id="results-grid">
            <!-- Results will be populated here -->
          </div>

          <!-- Loading State -->
          <div class="loading-state" id="loading-state" style="display: none;">
            <div class="spinner"></div>
            <p>Searching for interests...</p>
          </div>

          <!-- Empty State -->
          <div class="empty-state" id="empty-state">
            <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
              <circle cx="60" cy="60" r="50" stroke="#e2e8f0" stroke-width="2" fill="#f8fafc"/>
              <path d="M45 45L75 75M75 45L45 75" stroke="#cbd5e1" stroke-width="2" stroke-linecap="round"/>
              <circle cx="60" cy="60" r="30" stroke="#2563eb" stroke-width="2" fill="none"/>
              <path d="M80 80L90 90" stroke="#2563eb" stroke-width="3" stroke-linecap="round"/>
            </svg>
            <h3 class="empty-state-title">Start searching for interests</h3>
            <p class="empty-state-text">Enter keywords above to find relevant Facebook interests for your campaigns</p>
          </div>

          <!-- No Results State -->
          <div class="no-results-state" id="no-results-state" style="display: none;">
            <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
              <circle cx="60" cy="60" r="50" stroke="#e2e8f0" stroke-width="2" fill="#f8fafc"/>
              <path d="M40 50c0-11.046 8.954-20 20-20s20 8.954 20 20v10c0 11.046-8.954 20-20 20s-20-8.954-20-20V50z" fill="#fef3c7"/>
              <circle cx="50" cy="50" r="3" fill="#f59e0b"/>
              <circle cx="70" cy="50" r="3" fill="#f59e0b"/>
              <path d="M50 70c5.523 0 10-4.477 10-10" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
            </svg>
            <h3 class="empty-state-title">No interests found</h3>
            <p class="empty-state-text">Try adjusting your search terms or filters</p>
          </div>
        </div>
      </main>

      <!-- Interest Details Modal -->
      <div id="interest-details-modal" class="modal" style="display: none;">
        <div class="modal-content">
          <div class="modal-header">
            <h2 class="modal-title" id="interest-name">Interest Details</h2>
            <button class="modal-close">&times;</button>
          </div>
          
          <div class="modal-body">
            <div class="interest-details">
              <div class="detail-row">
                <span class="detail-label">Interest ID</span>
                <span class="detail-value" id="interest-id">-</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Category</span>
                <span class="detail-value" id="interest-category">-</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Audience Size</span>
                <span class="detail-value" id="interest-audience">-</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Description</span>
                <span class="detail-value" id="interest-description">-</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Related Topics</span>
                <div class="related-tags" id="related-topics">
                  <!-- Related topics will be populated here -->
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-actions">
            <button class="btn-secondary" id="close-details">Close</button>
            <button class="btn-primary" id="add-to-pool-from-details">Add to Pool</button>
          </div>
        </div>
      </div>
    </div>
  `;
}

export function initInterestSearchEnhancedPage() {
  const searchInput = document.getElementById('search-input');
  const searchBtn = document.getElementById('search-btn');
  const filtersToggle = document.getElementById('filters-toggle');
  const advancedFilters = document.getElementById('advanced-filters');
  const selectedInterests = new Set();
  
  // Search functionality
  async function performSearch() {
    const query = searchInput.value.trim();
    if (!query) return;
    
    // Show loading state
    document.getElementById('loading-state').style.display = 'flex';
    document.getElementById('empty-state').style.display = 'none';
    document.getElementById('no-results-state').style.display = 'none';
    document.getElementById('results-grid').innerHTML = '';
    document.getElementById('results-stats').style.display = 'none';
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock results
    const results = generateMockResults(query);
    
    // Hide loading
    document.getElementById('loading-state').style.display = 'none';
    
    if (results.length === 0) {
      document.getElementById('no-results-state').style.display = 'flex';
    } else {
      displayResults(results);
    }
  }
  
  // Display search results
  function displayResults(results) {
    const resultsGrid = document.getElementById('results-grid');
    const resultsStats = document.getElementById('results-stats');
    const resultCount = document.getElementById('result-count');
    
    resultCount.textContent = results.length;
    resultsStats.style.display = 'flex';
    
    resultsGrid.innerHTML = results.map(result => `
      <div class="interest-card" data-id="${result.id}">
        <div class="interest-header">
          <input type="checkbox" class="interest-checkbox" id="interest-${result.id}">
          <label for="interest-${result.id}" class="interest-label">
            <h3 class="interest-name">${result.name}</h3>
          </label>
          <button class="btn-icon interest-info" data-id="${result.id}" title="View details">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
              <path d="M5.255 5.786a.237.237 0 00.241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 00.25.246h.811a.25.25 0 00.25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
            </svg>
          </button>
        </div>
        
        <div class="interest-stats">
          <div class="stat">
            <span class="stat-label">Audience</span>
            <span class="stat-value">${result.audience}</span>
          </div>
          <div class="stat">
            <span class="stat-label">Category</span>
            <span class="stat-value">${result.category}</span>
          </div>
        </div>
        
        <p class="interest-description">${result.description}</p>
        
        <div class="interest-actions">
          <button class="btn-secondary btn-small quick-add" data-id="${result.id}">
            Quick Add
          </button>
          <button class="btn-primary btn-small view-suggestions" data-id="${result.id}">
            Get Suggestions
          </button>
        </div>
      </div>
    `).join('');
    
    // Add event handlers
    updateResultHandlers();
  }
  
  // Generate mock results
  function generateMockResults(query) {
    const mockData = [
      {
        id: 1,
        name: `${query} Enthusiasts`,
        audience: '2.4M',
        category: 'Interests',
        description: `People interested in ${query} and related topics`
      },
      {
        id: 2,
        name: `${query} Products`,
        audience: '1.8M',
        category: 'Shopping',
        description: `Consumers interested in ${query} products and brands`
      },
      {
        id: 3,
        name: `Online ${query}`,
        audience: '3.2M',
        category: 'Technology',
        description: `Digital and online ${query} activities`
      },
      {
        id: 4,
        name: `${query} Community`,
        audience: '890K',
        category: 'Community',
        description: `Active members of ${query} communities`
      },
      {
        id: 5,
        name: `${query} Trends`,
        audience: '1.5M',
        category: 'Lifestyle',
        description: `People following ${query} trends and news`
      }
    ];
    
    return mockData;
  }
  
  // Update result handlers
  function updateResultHandlers() {
    // Checkbox selection
    const checkboxes = document.querySelectorAll('.interest-checkbox');
    const selectedCount = document.getElementById('selected-count');
    const addSelectedBtn = document.getElementById('add-selected');
    
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const id = checkbox.id.replace('interest-', '');
        if (checkbox.checked) {
          selectedInterests.add(id);
        } else {
          selectedInterests.delete(id);
        }
        
        selectedCount.textContent = selectedInterests.size;
        addSelectedBtn.disabled = selectedInterests.size === 0;
      });
    });
    
    // Select all functionality
    document.getElementById('select-all').addEventListener('click', () => {
      const allChecked = Array.from(checkboxes).every(cb => cb.checked);
      checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        const id = cb.id.replace('interest-', '');
        if (cb.checked) {
          selectedInterests.add(id);
        } else {
          selectedInterests.delete(id);
        }
      });
      
      selectedCount.textContent = selectedInterests.size;
      addSelectedBtn.disabled = selectedInterests.size === 0;
    });
    
    // Interest info buttons
    document.querySelectorAll('.interest-info').forEach(btn => {
      btn.addEventListener('click', () => {
        showInterestDetails(btn.dataset.id);
      });
    });
    
    // Quick add buttons
    document.querySelectorAll('.quick-add').forEach(btn => {
      btn.addEventListener('click', () => {
        console.log('Quick add interest:', btn.dataset.id);
        showNotification('Interest added to pool!', 'success');
      });
    });
    
    // View suggestions buttons
    document.querySelectorAll('.view-suggestions').forEach(btn => {
      btn.addEventListener('click', () => {
        window.location.href = '/suggestions';
      });
    });
  }
  
  // Show interest details modal
  function showInterestDetails(interestId) {
    const modal = document.getElementById('interest-details-modal');
    
    // Populate modal with mock data
    document.getElementById('interest-name').textContent = 'Fashion Enthusiasts';
    document.getElementById('interest-id').textContent = interestId;
    document.getElementById('interest-category').textContent = 'Interests';
    document.getElementById('interest-audience').textContent = '2.4M - 2.8M';
    document.getElementById('interest-description').textContent = 'People who have expressed an interest in or like pages related to Fashion';
    
    // Add related topics
    const relatedTopics = ['Style', 'Clothing', 'Fashion Brands', 'Shopping'];
    document.getElementById('related-topics').innerHTML = relatedTopics
      .map(topic => `<span class="tag">${topic}</span>`)
      .join('');
    
    modal.style.display = 'flex';
  }
  
  // Event handlers
  searchInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
      performSearch();
    }
  });
  
  searchBtn.addEventListener('click', performSearch);
  
  // Filters toggle
  filtersToggle.addEventListener('click', () => {
    const isVisible = advancedFilters.style.display !== 'none';
    advancedFilters.style.display = isVisible ? 'none' : 'block';
    filtersToggle.classList.toggle('active');
  });
  
  // Reset filters
  document.getElementById('reset-filters').addEventListener('click', () => {
    document.getElementById('category-filter').value = '';
    document.getElementById('audience-size-filter').value = '';
    document.getElementById('country-filter').value = '';
    document.getElementById('sort-filter').value = 'relevance';
  });
  
  // Apply filters
  document.getElementById('apply-filters').addEventListener('click', () => {
    performSearch();
  });
  
  // Modal handlers
  const modal = document.getElementById('interest-details-modal');
  const modalClose = document.querySelector('.modal-close');
  const closeDetailsBtn = document.getElementById('close-details');
  
  [modalClose, closeDetailsBtn].forEach(element => {
    element.addEventListener('click', () => {
      modal.style.display = 'none';
    });
  });
  
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.style.display = 'none';
    }
  });
  
  // Bulk search button
  document.getElementById('bulk-search-btn').addEventListener('click', () => {
    console.log('Opening bulk search');
  });
  
  // View pool button
  document.getElementById('view-pool-btn').addEventListener('click', () => {
    window.location.href = '/pool';
  });
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  setTimeout(() => notification.classList.add('show'), 10);
  
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => notification.remove(), 300);
  }, 3000);
}