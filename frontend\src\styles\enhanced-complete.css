/* TargetWise Enhanced Complete Styles - Extracted from Enhanced HTML Files */

/* STANDARDIZED: Hero Section with design system gradient */
.hero-section {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important; /* STANDARDIZED: Design system gradient */
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

/* Decorative Circles - CRITICAL FIX: Force display with !important */
.hero-circle-1,
.hero-circle-2,
.welcome-circle-1,
.welcome-circle-2,
.welcome-circle-3 {
    position: absolute !important;
    border-radius: 50% !important;
    opacity: 0.1 !important;
    display: block !important;
}

.hero-circle-1 {
    width: 400px;
    height: 400px;
    background: white;
    top: -200px;
    right: -100px;
}

.hero-circle-2 {
    width: 300px;
    height: 300px;
    background: white;
    bottom: -150px;
    left: -50px;
}

/* Welcome Section Circles */
.welcome-circle-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -50px;
    background: rgba(255, 255, 255, 0.1);
}

.welcome-circle-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: -75px;
    background: rgba(255, 255, 255, 0.1);
}

.welcome-circle-3 {
    width: 100px;
    height: 100px;
    top: 50%;
    right: 10%;
    background: rgba(255, 255, 255, 0.1);
}

/* Floating Action Button - CRITICAL FIX: Force display with !important */
.fab {
    position: fixed !important;
    bottom: 24px !important;
    right: 24px !important;
    width: 56px !important;
    height: 56px !important;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 24px !important;
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3) !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
    z-index: 1000 !important;
    border: none !important;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 32px rgba(37, 99, 235, 0.4);
}

/* API Counter - CRITICAL FIX: Force display with !important */
.api-counter {
    position: fixed !important;
    bottom: 80px !important;
    right: 24px !important;
    background: white !important;
    border-radius: 12px !important;
    padding: 12px 20px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    z-index: 999 !important;
}

.api-label {
    color: #64748b;
    font-size: 14px;
}

.api-value {
    font-size: 18px;
    font-weight: 700;
    color: #2563eb;
}

/* Tool Cards Enhanced Hover Effect */
.tool-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s;
}

.tool-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.tool-card:hover::before {
    transform: scaleX(1);
}

/* Stats Cards with Gradient Icons */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

/* Gradient backgrounds for stat icons */
.stat-icon-blue {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.stat-icon-green {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.stat-icon-yellow {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.stat-icon-purple {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
}

/* Search History Sidebar Items */
.history-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.history-item:hover {
    background: #f8fafc;
    transform: translateX(4px);
}

.history-icon {
    width: 32px;
    height: 32px;
    background: #f0f9ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

/* Enhanced Form Elements */
.file-upload-area {
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
    padding: 48px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
    background: #fafbfc;
}

.file-upload-area:hover {
    border-color: #2563eb;
    background: #f0f9ff;
}

.file-upload-area.active {
    border-color: #2563eb;
    background: #f0f9ff;
    border-style: solid;
}

/* Enhanced Buttons */
.btn-submit {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    padding: 16px 48px;
    border: none;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
    display: inline-flex;
    align-items: center;
    gap: 12px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.35);
}

.btn-submit:active {
    transform: translateY(0);
}

/* Category Badges */
.category-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.category-badge.fitness {
    background: #dcfce7;
    color: #166534;
}

.category-badge.technology {
    background: #dbeafe;
    color: #1e40af;
}

.category-badge.shopping {
    background: #fce7f3;
    color: #9f1239;
}

/* Enhanced Table Styles */
.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th {
    text-align: left;
    padding: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #64748b;
    border-bottom: 1px solid #f1f5f9;
    background: #f8fafc;
}

.results-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #f8fafc;
}

.results-table tr:hover {
    background: #fafbfc;
}

/* Suggestion Cards */
.suggestion-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #f1f5f9;
    transition: all 0.2s;
}

.suggestion-item:hover {
    border-color: #e2e8f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.suggestion-item.selected {
    border-color: #2563eb;
    background: #f0f9ff;
}

/* Bulk Actions Bar */
.bulk-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transform: translateY(100%);
    transition: transform 0.3s;
    z-index: 100;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
}

.bulk-actions.show {
    transform: translateY(0);
}

/* Collection Info Banner */
.collection-info {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
}

.collection-icon {
    width: 48px;
    height: 48px;
    background: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
}

/* Loading States */
.loading-state {
    text-align: center;
    padding: 60px;
    color: #64748b;
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #e2e8f0;
    border-top-color: #2563eb;
    border-radius: 50%;
    margin: 0 auto 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 40px;
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success Message */
.success-message {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 2px solid #86efac;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    animation: slideDown 0.3s ease-out;
}

/* Page Icon Enhancement */
.page-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.25);
}

/* Sidebar Active State */
.sidebar-item.active {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    color: #2563eb;
}