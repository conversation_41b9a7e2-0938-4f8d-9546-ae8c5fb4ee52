document.addEventListener('DOMContentLoaded', () => {
  const elements = document.querySelectorAll('button.btn, a.btn');
  elements.forEach(el => {
    el.classList.add('tw-btn');
    const mapping = {
      'btn-primary': 'tw-btn-primary',
      'btn-secondary': 'tw-btn-secondary',
      'btn-success': 'tw-btn-success',
      'btn-danger': 'tw-btn-danger',
      'btn-warning': 'tw-btn-warning',
      'btn-info': 'tw-btn-info',
      'btn-outline-primary': 'tw-btn-outline-primary',
      'btn-outline-secondary': 'tw-btn-outline-secondary',
      'btn-outline-success': 'tw-btn-success',
      'btn-outline-danger': 'tw-btn-danger',
      'btn-outline-warning': 'tw-btn-warning',
      'btn-outline-info': 'tw-btn-info'
    };
    Object.entries(mapping).forEach(([bs, tw]) => {
      if (el.classList.contains(bs)) {
        el.classList.add(tw);
      }
    });
    if (el.classList.contains('btn-sm')) el.classList.add('tw-btn-sm');
    if (el.classList.contains('btn-lg')) el.classList.add('tw-btn-lg');
  });
});
