/**
 * Enhanced Pages Styles
 * Complete styling for all TargetWise pages following the design system
 */

/* ====================== */
/* Base & Reset */
/* ====================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: #f8fafc;
  color: #1a1a2e;
  line-height: 1.6;
  min-height: 100vh;
}

/* ====================== */
/* Typography */
/* ====================== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: #1e293b;
}

h1 { font-size: 36px; }
h2 { font-size: 32px; }
h3 { font-size: 28px; }
h4 { font-size: 20px; font-weight: 600; }
h5 { font-size: 18px; font-weight: 600; }
h6 { font-size: 16px; font-weight: 600; }

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: #64748b;
  font-weight: 400;
}

/* ====================== */
/* Header */
/* ====================== */
.header {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #2563eb;
  text-decoration: none;
  transition: transform 0.2s;
}

.logo:hover {
  transform: translateY(-1px);
}

.logo svg {
  width: 32px;
  height: 32px;
}

/* Navigation */
.nav-menu {
  display: flex;
  gap: 8px;
  align-items: center;
}

.nav-link {
  padding: 8px 16px;
  text-decoration: none;
  color: #64748b;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s;
}

.nav-link:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.08);
}

.nav-link.active {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: none;
  background: #f1f5f9;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  color: #64748b;
}

.theme-toggle:hover {
  background: #e2e8f0;
  color: #475569;
}

/* ====================== */
/* Layout */
/* ====================== */
.main-content {
  min-height: calc(100vh - 80px);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.page-actions {
  display: flex;
  gap: 12px;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  min-height: calc(100vh - 80px);
}

.sidebar {
  width: 260px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.sidebar-nav {
  flex: 1;
  padding: 16px;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #64748b;
  text-decoration: none;
  border-radius: 8px;
  margin-bottom: 4px;
  transition: all 0.2s;
}

.sidebar-link:hover {
  background: #f8fafc;
  color: #475569;
}

.sidebar-link.active {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
}

.sidebar-footer {
  padding: 24px;
  border-top: 1px solid #e2e8f0;
}

.dashboard-main {
  flex: 1;
  padding: 32px;
  background: #f8fafc;
  overflow-y: auto;
}

/* ====================== */
/* Cards & Containers */
/* ====================== */
.card, .form-card, .info-card, .stat-card, .pool-card, .interest-card, .suggestion-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s;
}

.card:hover, .info-card:hover, .stat-card:hover, .pool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

/* ====================== */
/* Forms */
/* ====================== */
.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 14px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s;
  background: white;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-hint {
  font-size: 14px;
  color: #94a3b8;
  margin-top: 4px;
}

/* File Upload */
.upload-area {
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  padding: 48px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: #f8fafc;
}

.upload-area:hover {
  border-color: #94a3b8;
  background: #f1f5f9;
}

.upload-area.drag-over {
  border-color: #2563eb;
  background: rgba(37, 99, 235, 0.05);
}

.upload-icon {
  color: #94a3b8;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #475569;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 14px;
  color: #94a3b8;
}

/* ====================== */
/* Buttons */
/* ====================== */
.btn, .btn-primary, .btn-secondary, .btn-text, .btn-icon {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.btn-text {
  background: transparent;
  color: #2563eb;
  padding: 8px 16px;
}

.btn-text:hover {
  background: rgba(37, 99, 235, 0.08);
}

.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 8px;
  background: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-icon:hover {
  background: #f8fafc;
  color: #475569;
}

.btn-small {
  padding: 8px 16px;
  font-size: 14px;
}

/* Submit Button */
.btn-submit {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  padding: 16px 48px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
  display: inline-flex;
  align-items: center;
  gap: 12px;
}

.btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.35);
}

/* ====================== */
/* Search Components */
/* ====================== */
.search-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 24px;
}

.search-container {
  margin-bottom: 20px;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 20px;
  color: #94a3b8;
}

.search-input {
  flex: 1;
  padding: 16px 20px 16px 56px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
}

/* Advanced Filters */
.advanced-filters-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: #64748b;
  font-weight: 500;
}

.advanced-filters-toggle:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.advanced-filters-toggle.active {
  background: #f8fafc;
  color: #2563eb;
  border-color: #2563eb;
}

.advanced-filters {
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
  margin-top: 24px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.filter-group label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 8px;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
}

/* ====================== */
/* Results & Stats */
/* ====================== */
.results-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.results-count {
  font-size: 16px;
  color: #64748b;
}

.results-count strong {
  color: #1e293b;
  font-weight: 600;
}

.results-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.badge {
  background: #2563eb;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 4px;
}

/* Grids */
.results-grid, .suggestions-grid, .pools-grid, .info-cards, .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.interest-card, .suggestion-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s;
}

.interest-card:hover, .suggestion-card:hover {
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
}

.interest-header, .suggestion-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.interest-checkbox, .suggestion-checkbox {
  margin-top: 4px;
}

.interest-label, .suggestion-label {
  flex: 1;
  cursor: pointer;
}

.interest-name, .suggestion-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.interest-stats, .suggestion-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #475569;
}

.interest-description, .suggestion-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 16px;
}

.interest-actions, .suggestion-actions {
  display: flex;
  gap: 12px;
}

/* ====================== */
/* Pool Cards */
/* ====================== */
.pool-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s;
}

.pool-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.pool-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pool-menu {
  position: relative;
}

.menu-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #94a3b8;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: #f8fafc;
  color: #64748b;
}

.pool-name {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.pool-description {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 20px;
  line-height: 1.5;
}

.pool-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 20px;
}

.pool-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.pool-actions {
  display: flex;
  gap: 12px;
}

/* Add Pool Card */
.add-pool-card {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.2s;
}

.add-pool-card:hover {
  border-color: #94a3b8;
  background: #f1f5f9;
  transform: translateY(0);
  box-shadow: none;
}

.add-pool-content {
  text-align: center;
}

.add-pool-text {
  margin-top: 16px;
  color: #64748b;
  font-weight: 500;
}

/* ====================== */
/* Stat Cards */
/* ====================== */
.stat-card {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.negative {
  color: #ef4444;
}

/* ====================== */
/* Activity & Tables */
/* ====================== */
.activity-section {
  margin-top: 48px;
}

.activity-list {
  margin-top: 24px;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f1f5f9;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.activity-icon.info {
  background: rgba(37, 99, 235, 0.1);
  color: #2563eb;
}

.activity-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #475569;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 13px;
  color: #94a3b8;
}

/* Tables */
.sheets-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead {
  background: #f8fafc;
}

th {
  padding: 16px 24px;
  text-align: left;
  font-weight: 600;
  color: #475569;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

td {
  padding: 20px 24px;
  border-top: 1px solid #f1f5f9;
}

tbody tr:hover {
  background: #f8fafc;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* ====================== */
/* Modals */
/* ====================== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 24px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-large {
  max-width: 800px;
}

.modal-header {
  padding: 32px 32px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.modal-close {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  color: #94a3b8;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #f8fafc;
  color: #475569;
}

.modal-body {
  padding: 32px;
}

.modal-form .form-group {
  margin-bottom: 24px;
}

.modal-actions {
  padding: 24px 32px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Success Modal */
.success-icon {
  text-align: center;
  margin-bottom: 24px;
}

.modal-text {
  font-size: 16px;
  color: #64748b;
  text-align: center;
  margin-bottom: 32px;
}

/* ====================== */
/* Empty States */
/* ====================== */
.empty-state, .no-results-state, .loading-state {
  text-align: center;
  padding: 80px 24px;
}

.empty-state-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 24px 0 12px;
}

.empty-state-text {
  font-size: 16px;
  color: #64748b;
  max-width: 400px;
  margin: 0 auto;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top-color: #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ====================== */
/* Notifications */
/* ====================== */
.notification {
  position: fixed;
  top: 24px;
  right: 24px;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  transform: translateX(400px);
  transition: transform 0.3s ease;
  z-index: 3000;
  font-weight: 500;
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  background: #10b981;
  color: white;
}

.notification-error {
  background: #ef4444;
  color: white;
}

.notification-info {
  background: #2563eb;
  color: white;
}

/* ====================== */
/* Utility Classes */
/* ====================== */
.mb-6 { margin-bottom: 48px; }
.mb-4 { margin-bottom: 32px; }
.mb-3 { margin-bottom: 24px; }
.mb-2 { margin-bottom: 16px; }
.mb-1 { margin-bottom: 8px; }

.text-center { text-align: center; }
.text-right { text-align: right; }

.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.gap-1 { gap: 8px; }
.gap-2 { gap: 16px; }
.gap-3 { gap: 24px; }
.gap-4 { gap: 32px; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }

.w-100 { width: 100%; }

/* ====================== */
/* Responsive */
/* ====================== */
@media (max-width: 1024px) {
  .dashboard-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 16px;
  }
  
  .sidebar-link {
    white-space: nowrap;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .page-actions {
    width: 100%;
  }
  
  .page-actions button {
    flex: 1;
  }
  
  .results-grid,
  .suggestions-grid,
  .pools-grid,
  .info-cards,
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    padding: 12px 16px;
  }
  
  .nav-menu {
    display: none;
  }
  
  .container {
    padding: 24px 16px;
  }
  
  .modal-content {
    margin: 16px;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .age-range {
    grid-template-columns: 1fr;
  }
  
  .range-separator {
    display: none;
  }
}