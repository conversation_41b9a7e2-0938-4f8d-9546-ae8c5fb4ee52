.PHONY: help install install-dev install-test test test-cov lint format check format-check clean clean-pyc clean-build clean-test

# Default target
help:
	@echo "TargetWise - Facebook Algorithmic Targeting 2.0"
	@echo ""
	@echo "Available targets:"
	@echo "  install      : Install the package in development mode with all dependencies"
	@echo "  install-dev  : Install development dependencies"
	@echo "  install-test : Install test dependencies"
	@echo "  test        : Run tests"
	@echo "  test-cov    : Run tests with coverage"
	@echo "  lint        : Run linters"
	@echo "  format      : Format code"
	@echo "  check       : Run all checks (lint, format, test)"
	@echo "  clean       : Remove all build, test, coverage and Python artifacts"
	@echo "  clean-pyc   : Remove Python file artifacts"
	@echo "  clean-build : Remove build artifacts"
	@echo "  clean-test  : Remove test and coverage artifacts"

# Installation
install:
	@echo "Installing TargetWise in development mode..."
	pip install -e .


install-dev:
	@echo "Installing development dependencies..."
	pip install -e ".[dev]"
	pre-commit install

install-test:
	@echo "Installing test dependencies..."
	pip install -e ".[test]"


# Testing
test:
	@echo "Running tests..."
	pytest

test-cov:
	@echo "Running tests with coverage..."
	pytest --cov=app --cov-report=term-missing

# Linting and Formatting
lint:
	@echo "Running linters..."
	flake8 app
	mypy app

format:
	@echo "Formatting code..."
	black app
	isort app

check:
	@echo "Running all checks..."
	make lint
	make format-check
	make test

format-check:
	@echo "Checking code formatting..."
	black --check app
	isort --check-only app

# Cleaning
clean: clean-build clean-pyc clean-test

clean-pyc:
	@echo "Removing Python file artifacts..."
	find . -type d -name '__pycache__' -exec rm -rf {} +
	find . -type f -name '*.py[co]' -delete
	find . -type f -name '*~' -delete
	find . -type f -name '*.swp' -delete

clean-build:
	@echo "Removing build artifacts..."
	rm -rf build/ dist/ *.egg-info/ .eggs/ .pytest_cache/ .mypy_cache/

clean-test:
	@echo "Removing test and coverage artifacts..."
	rm -f .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	find . -type d -name '.pytest_cache' -exec rm -rf {} +

# Docker
docker-build:
	@echo "Building Docker image..."
	docker build -t targetwise .

docker-run:
	@echo "Running Docker container..."
	docker run -p 8000:8000 --env-file .env targetwise

docker-compose-up:
	@echo "Starting services with Docker Compose..."
	docker-compose up --build -d

docker-compose-down:
	@echo "Stopping services with Docker Compose..."
	docker-compose down

# Database
db-init:
	@echo "Initializing database..."
	python -m app.db.init_db

db-migrate:
	@echo "Running database migrations..."
	alembic upgrade head

db-downgrade:
	@echo "Reverting database migration..."
	alembic downgrade -1

# Documentation
docs:
	@echo "Building documentation..."
	cd docs && make html

# Development server
run:
	@echo "Starting development server..."
	uvicorn app.main:app --reload

run-prod:
	@echo "Starting production server..."
	gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app
