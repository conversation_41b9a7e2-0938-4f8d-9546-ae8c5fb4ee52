/**
 * Minimal App Test - No imports
 */

console.log('Minimal app starting...');

// Simple test function
function initMinimalApp() {
  console.log('Initializing minimal app...');
  
  const appElement = document.getElementById('app');
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
        <h1 style="color: #2563eb;">✅ JavaScript Module Working!</h1>
        <p>The module system is functioning correctly.</p>
        <p>Time: ${new Date().toLocaleTimeString()}</p>
      </div>
    `;
    console.log('Minimal app initialized successfully');
  } else {
    console.error('App element not found');
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initMinimalApp);
} else {
  initMinimalApp();
}

export { initMinimalApp };
