/**
 * Header Component - Shared application header
 * Used across all pages for consistent navigation
 */

export function Header({ currentPage = 'home' }) {
  const navItems = [
    { id: 'home', label: 'Home', href: '/' },
    { id: 'micro-tools', label: 'Micro-Tools', href: '/micro-tools' },
    { id: 'documentation', label: 'Documentation', href: '/docs' },
    { id: 'pricing', label: 'Pricing', href: '/pricing' },
    { id: 'admin', label: 'Admin', href: '/admin' }
  ];

  return `
    <header class="app-header">
      <div class="app-header-content">
        <a href="/" class="app-logo">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <circle cx="12" cy="12" r="6"></circle>
            <circle cx="12" cy="12" r="2"></circle>
          </svg>
          TargetWise
        </a>
        
        <nav class="flex items-center gap-2 lg:flex sm:hidden">
          ${navItems.map(item => `
            <a href="${item.href}" 
               class="nav-link ${currentPage === item.id ? 'active' : ''}"
               data-nav-item="${item.id}">
              ${item.label}
            </a>
          `).join('')}
        </nav>
        
        <div class="flex items-center gap-3">
          <button class="btn-icon btn-secondary theme-toggle" title="Toggle theme">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
          </button>
          <a href="/login" class="btn btn-outline sm:hidden">Log In</a>
          <a href="/signup" class="btn btn-primary">Sign Up Free</a>
        </div>
      </div>
    </header>
  `;
}

// Initialize header interactions
export function initHeader() {
  // Theme toggle functionality
  const themeToggle = document.querySelector('.theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      // Theme toggle logic would go here
      console.log('Theme toggle clicked');
    });
  }

  // Mobile menu toggle (if needed)
  // Add mobile navigation logic here
}

export default Header;