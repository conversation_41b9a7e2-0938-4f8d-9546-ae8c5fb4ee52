<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug TargetWise</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9fafb;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .text-center { text-align: center; }
        .text-gray-600 { color: #4b5563; }
        .mb-4 { margin-bottom: 1rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p class="text-gray-600">Testing imports...</p>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" style="display: none;"></div>

    <!-- Load the debug app module -->
    <script type="module">
        console.log('Starting debug app...');
        
        try {
            // Import the debug app
            const { initDebugApp } = await import('./debug-app.js');
            console.log('Debug app module imported successfully');
            
            // Hide loading screen
            document.getElementById('loading-screen').style.display = 'none';
            document.getElementById('app').style.display = 'block';
            
            console.log('Loading screen hidden, debug app shown');
            
        } catch (error) {
            console.error('Failed to load debug app:', error);
            
            // Show error message
            document.getElementById('loading-screen').innerHTML = `
                <div class="text-center">
                    <h1 style="color: red;">❌ Debug App Loading Failed</h1>
                    <p>Error: ${error.message}</p>
                    <pre style="text-align: left; background: #f5f5f5; padding: 10px; border-radius: 4px; max-width: 600px;">${error.stack}</pre>
                </div>
            `;
        }
    </script>
</body>
</html>
