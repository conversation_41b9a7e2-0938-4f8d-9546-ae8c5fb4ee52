{"name": "targetwise", "version": "1.0.0", "description": "Facebook Algorithmic Targeting 2.0", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "format": "prettier --write \"src/**/*.{js,css,html}\"", "clean": "<PERSON><PERSON><PERSON> dist", "notify": "node infrastructure/scripts/notifications.js", "prepare": "husky install"}, "jest": {"testEnvironment": "jsdom", "testMatch": ["**/tests/**/*.test.js"], "testPathIgnorePatterns": ["<rootDir>/tests/e2e/"], "transform": {"^.+\\.js$": "babel-jest"}, "moduleNameMapper": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}, "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "dependencies": {"bootstrap": "^5.3.2", "chalk": "^5.3.0", "chart.js": "^4.4.1", "gridjs": "^6.0.6", "ora": "^8.0.0"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.8", "@playwright/test": "^1.40.1", "@vitejs/plugin-legacy": "^5.2.0", "babel-jest": "^29.7.0", "eslint": "^8.56.0", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.4", "rimraf": "^5.0.0", "vite": "^5.0.10", "typescript": "^5.4.2"}}