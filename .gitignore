# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
.pytest_cache/
.pyenv/
.python-version
pip-log.txt
pip-delete-this-directory.txt
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.venv/

# Node/JavaScript
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# IDEs and Editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-*
*.code-workspace

# Logs
*.log
logs/
server_log.txt

# Environment variables
.env
.env.*
!.env.example

# Testing
.coverage
htmlcov/
.pytest_cache/

# Build output
/frontend/dist/
/backend/build/
/static/css/*.css
/static/js/*.js
!static/css/README.md
!static/js/README.md

# Temporary directories
temp/*
!temp/.gitkeep
!temp/README.md
tmp/
output/*
!output/.gitkeep

# Local development
local_settings.py
docker-compose.override.yml

# Jupyter Notebook
.ipynb_checkpoints/

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
.idea_modules/
*.iml
!output/README.md
*.tmp
# Runtime data/config (auto-generated; do not version)
data/
data/credentials.json
data/settings.json
data/status.json
# Keep template files
!data/*.template.json

# Test files
.pytest_cache/
.coverage
htmlcov/
pytest_output.txt
pytest_bulk_output.txt
tests/playwright/__screenshots__/

# Generated files
targeting_sheet*.xlsx
downloaded_sample.csv

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Redis
dump.rdb

# Test results
test-results/

# Backup files
backup/

# Browser test artifacts
playwright-report/
.playwright/

# Jest coverage
coverage/

# Local development files
.local/
.history/
*.bak

# Pre-commit
.pre-commit-config.yaml

# Editor specific
.vscode/settings.json
.idea/workspace.xml
.idea/tasks.xml

# OS specific
Thumbs.db
.DS_Store
desktop.ini
