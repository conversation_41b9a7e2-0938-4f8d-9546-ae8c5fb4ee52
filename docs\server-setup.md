# TargetWise Server Setup and Running Guide

## Overview

TargetWise is a dual-server application consisting of:
- **Backend**: FastAPI server for API endpoints and Facebook Marketing API integration
- **Frontend**: Static file server for the web interface

## Prerequisites

### Required Software
- **Python 3.8+** (tested with Python 3.12+)
- **Node.js 16+** (optional, for development)
- **npm** (optional, for development)

### System Requirements
- Windows 10/11, macOS, or Linux
- 4GB RAM minimum
- Internet connection for Facebook API calls

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd TargetWise
```

### 2. Install Python Dependencies
```bash
# Install base dependencies
pip install -r requirements/base.txt

# For development (optional)
pip install -r requirements/development.txt
```

### 3. Environment Configuration
Create a `.env` file in the root directory:
```env
# Facebook API Configuration
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_ACCESS_TOKEN=your_access_token
FACEBOOK_AD_ACCOUNT_ID=your_ad_account_id

# Server Configuration
DEBUG=True
LOG_LEVEL=INFO
```

## Running the Application

### Method 1: Quick Start (Recommended)

#### Start Backend Server
```bash
# From project root directory
python run.py
```
**Expected Output:**
```
INFO:     Will watch for changes in these directories: ['C:\...\TargetWise']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using WatchFiles
```

#### Start Frontend Server
```bash
# Navigate to frontend directory
cd frontend

# Start the Python HTTP server
python server.py
```
**Expected Output:**
```
Server running at http://localhost:8080/
Press Ctrl+C to stop
```

### Method 2: Development Mode (Advanced)

#### Backend (FastAPI with auto-reload)
```bash
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

#### Frontend (Vite development server)
```bash
cd frontend
npm install  # First time only
npm run dev
```

## Server URLs and Ports

| Service | URL | Purpose |
|---------|-----|---------|
| Frontend | http://localhost:8080 | Main web interface |
| Backend API | http://localhost:8000 | REST API endpoints |
| API Docs | http://localhost:8000/docs | Interactive API documentation |
| Admin Panel | http://localhost:8080/admin | Configuration interface |

## Verification Steps

### 1. Check Backend Health
```bash
curl http://localhost:8000/health
```
Expected response: `{"status": "healthy"}`

### 2. Check Frontend Access
Open browser and navigate to: http://localhost:8080

### 3. Verify API Documentation
Open browser and navigate to: http://localhost:8000/docs

## Troubleshooting

### Common Issues

#### Backend Won't Start
**Error**: `ModuleNotFoundError: No module named 'pydantic_settings'`
**Solution**: 
```bash
pip install pydantic-settings
```

#### Frontend Server Issues
**Error**: `Can't open file 'server.py'`
**Solution**: Ensure you're in the frontend directory
```bash
cd frontend
python server.py
```

#### Port Already in Use
**Error**: `Address already in use`
**Solution**: 
```bash
# Find and kill process using port 8000
netstat -ano | findstr :8000  # Windows
lsof -ti:8000 | xargs kill    # macOS/Linux

# Or use different ports
uvicorn api.main:app --port 8001
```

#### Facebook API Connection Issues
**Error**: API calls failing
**Solution**: 
1. Verify credentials in admin panel (http://localhost:8080/admin)
2. Check Facebook access token validity
3. Ensure ad account ID is correct

### Log Files
- Backend logs: `backend.log`
- Frontend logs: `frontend/frontend.log`
- Application logs: `logs/app.log`

## Development Workflow

### 1. Start Development Servers
```bash
# Terminal 1: Backend
python run.py

# Terminal 2: Frontend
cd frontend && python server.py
```

### 2. Making Changes
- **Backend changes**: Auto-reload enabled, changes reflect immediately
- **Frontend changes**: Refresh browser to see updates
- **Configuration changes**: Restart servers

### 3. Testing
```bash
# Run backend tests
python -m pytest tests/

# Run frontend tests (if npm setup)
cd frontend && npm test
```

## Production Deployment

### Environment Variables
```env
DEBUG=False
LOG_LEVEL=WARNING
FACEBOOK_ACCESS_TOKEN=production_token
```

### Start Commands
```bash
# Backend (production)
uvicorn api.main:app --host 0.0.0.0 --port 8000 --workers 4

# Frontend (production)
cd frontend && python server.py
```

## Stopping the Servers

### Graceful Shutdown
- Press `Ctrl+C` in each terminal running the servers
- Wait for "Shutting down" messages

### Force Stop (if needed)
```bash
# Windows
taskkill /f /im python.exe

# macOS/Linux
pkill -f "python run.py"
pkill -f "python server.py"
```

## Next Steps

After successfully running the servers:
1. Configure Facebook API credentials via admin panel
2. Test interest search functionality
3. Review user guide: `docs/user-guide.md`
4. Check API documentation at http://localhost:8000/docs

## Support

For issues:
1. Check logs in respective log files
2. Verify all prerequisites are installed
3. Ensure ports 8000 and 8080 are available
4. Review troubleshooting section above
