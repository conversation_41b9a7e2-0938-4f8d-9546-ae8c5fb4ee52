# // DOC: docs/micro-tools/search-bulk-feature.md
import logging
import os
import sys
from datetime import datetime
from typing import List, Optional
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

import uvicorn
from pathlib import Path
from fastapi import BackgroundTasks, Depends, FastAPI, File, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.requests import Request
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.templating import Jinja2Templates

from api.api.v1 import admin_router, api_router
from api.config.settings import settings
from api.models.admin import AdminStore
from api.utils.notifications import (
    notify_info, notify_success, notify_warning, notify_error, notify_debug,
    NotificationLevel
)

# Define logs directory and log file path
LOGS_DIR = Path(project_root) / "logs"
LOG_FILE = LOGS_DIR / "app.log"

# Create logs directory if it doesn't exist
LOGS_DIR.mkdir(parents=True, exist_ok=True)

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
logger.info("TargetWise API server is starting up")

# Initialize FastAPI with metadata
app = FastAPI(
    title="TargetWise API",
    description="API for building 12-column Algorithmic Targeting sheets using Meta Marketing API",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    on_startup=[lambda: notify_info("API server starting up", context="startup")],
    on_shutdown=[lambda: notify_info("API server shutting down", context="shutdown")],
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Frontend dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware to log all incoming requests and notify about important events."""
    path = request.url.path
    # Skip logging for static assets and SSE endpoints
    if path == "/favicon.ico" or path.startswith("/api/notifications/stream"):
        return await call_next(request)

    notify_info(f"Incoming request: {request.method} {request.url}",
                context="http",
                data={"method": request.method, "url": str(request.url)})
    
    try:
        response = await call_next(request)
        
        # Log response status
        if response.status_code >= 500:
            notify_error(
                f"Server error: {request.method} {request.url} - {response.status_code}",
                context="http",
                data={"status_code": response.status_code}
            )
        elif response.status_code >= 400:
            notify_warning(
                f"Client error: {request.method} {request.url} - {response.status_code}",
                context="http",
                data={"status_code": response.status_code}
            )
            
        return response
    except Exception as e:
        notify_error(
            f"Unhandled exception: {str(e)}",
            context="http",
            data={"error": str(e), "path": request.url.path}
        )
        raise

# Set up templates for remaining admin pages
BASE_APP_DIR = Path(__file__).resolve().parent
templates = Jinja2Templates(directory=str(BASE_APP_DIR / "templates"))

# Include API routes
app.include_router(api_router, prefix="/api")

# Include Admin routes
app.include_router(admin_router)

# Initialize admin store and load persisted settings
admin_store = AdminStore()
app_settings = admin_store.get_settings()

# Set environment variables from stored settings
if app_settings:
    os.environ["TARGET_AUDIENCE_SIZE_MIN"] = str(app_settings.target_audience_min)
    os.environ["TARGET_AUDIENCE_SIZE_MAX"] = str(app_settings.target_audience_max)


@app.get("/", response_class=HTMLResponse)
async def root():
    """API root endpoint."""
    return HTMLResponse(content="""
    <html>
        <head>
            <title>TargetWise API</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: #f8fafc;
                }
                h1 { color: #1e293b; }
                a {
                    color: #2563eb;
                    text-decoration: none;
                    padding: 0.5rem 1rem;
                    background: #e0f2fe;
                    border-radius: 0.5rem;
                    display: inline-block;
                    margin: 0.5rem;
                }
                a:hover { background: #bae6fd; }
                .links { margin-top: 2rem; }
            </style>
        </head>
        <body>
            <h1>🎯 TargetWise API v2.0</h1>
            <p>Backend API for Facebook Algorithmic Targeting</p>
            <div class="links">
                <a href="/api/docs">📚 API Documentation</a>
                <a href="/admin">⚙️ Admin Dashboard</a>
                <a href="/logs">📋 View Logs</a>
            </div>
        </body>
    </html>
    """)


@app.get("/logs", response_class=HTMLResponse)
async def logs_viewer(request: Request):
    """Serve the logs viewer page with enhanced visualization."""
    notify_info("Accessing logs viewer", context="system")
    return templates.TemplateResponse(
        "logs.html", {"request": request, "now": datetime.now()}
    )


if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)