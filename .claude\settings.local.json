{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"seedSearch|addSeedBtn|currentSeeds|generateSuggestionsBtn\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/micro-tools/suggestions.html)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"Search for interests\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/micro-tools/suggestions.html -A3 -B3)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"Loading State|loading-state\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/micro-tools/suggestions.html)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"<script\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/base.html)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"script.*src=|import\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/micro-tools/search.html)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"nav-tabs|tab-pane\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/micro-tools/search.html -A2 -B2)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"Single Search|Bulk Search\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/micro-tools/search.html -A5 -B5)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"main-content\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/app/templates/base.html)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"font-family|--font\" /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/TargetWise/static/css/pages/micro-tools.css)", "Bash(rm:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(npm test)", "Bash(npm run build:ts:*)", "Bash(kill:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(chmod:*)", "Bash(git rm:*)", "Bash(find:*)", "<PERSON><PERSON>(git mv:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(touch:*)", "Bash(git add:*)", "Bash(git branch:*)", "Bash(tree:*)", "<PERSON><PERSON>(mv:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:localhost)", "<PERSON><PERSON>(pkill:*)", "mcp__playwright__browser_navigate"], "deny": []}}