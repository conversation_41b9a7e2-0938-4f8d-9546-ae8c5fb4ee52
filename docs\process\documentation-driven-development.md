### 📑 Documentation-Driven Development (DDD) – Team Instruction Set  

> **Purpose**   
> Establish a rock-solid, self-updating knowledge base that every human developer *and* every AI coding assistant can rely on. This workflow dramatically reduces hallucinations and defects, speeds up delivery, and preserves collective context as our SaaS grows.

---

#### 1. Core Principles  

| # | Principle | Why it matters |
|---|-----------|----------------|
| 1 | **Write First, Build Second** | A clear spec forces clarity of thought and gives the AI concrete boundaries. |
| 2 | **Single Source of Truth** | Every feature gets its *own* doc; updates flow *into* that doc—never into scattered chats. |
| 3 | **Continuous Doc Sync** | After **every** code change, the AI (or developer) must append the change to the relevant doc section. |
| 4 | **Reference, Don’t Repeat** | New prompts must *explicitly* cite existing docs so the AI reasons with full context. |
| 5 | **Version & Link** | Docs live in `/docs` (Markdown); code comments link back to their doc section (`@see docs/stripe/integration.md`). |

---

#### 2. Required Document Types & Minimum Contents  

| Doc | When to create | Must include |
|-----|----------------|--------------|
| **Feature Brief** (`docs/<feature>/brief.md`) | Before any code is written | Goal, user flow, acceptance criteria, success metrics |
| **Tech Spec** (`docs/<feature>/spec.md`) | Immediately after brief approval | Data models, API contracts, UX wireframes, edge cases |
| **Implementation Log** (`docs/<feature>/log.md`) | Auto-updated after every task | Task description, diff link/commit hash, date, owner |
| **ADR** (`docs/adr/NNN-title.md`) | When a key architectural decision is made | Context, decision, consequences, alternatives |
| **How-To / Runbook** (`docs/<feature>/how-to.md`) | Feature reaches QA-ready | Setup, env vars, common troubleshooting steps |

> **Template snippets** live in `docs/_templates/`. Re-use them—no free-form docs.

---

#### 3. Standard Workflow for *Every* Task  

1. **Create / update Feature Brief**  
   - _Prompt example_:  
     > “AI, draft a Feature Brief for **In-App Notifications**. Include goal, user flow, requirements, roadmap, and recommended tech stack.”  
     Commit the returned draft; request human review.

2. **Generate / merge Roadmap section**  
   - Each roadmap item becomes a GitHub issue linked back to the doc.

3. **Execute first roadmap task**  
   - _Prompt example_:  
     > “AI, implement **registration page** per the Feature Brief. When done, append a summary of what changed to `docs/auth/log.md`.”

4. **Doc-First QA**  
   - QA verifies both functionality **and** doc accuracy. Bugs or doc gaps block merging.

5. **Iterate**  
   - For subsequent changes, always start the prompt with:  
     > “Consult the existing docs in `/docs/...`; update them after making changes.”

---

#### 4. Tooling & Conventions  

| Area | Standard |
|------|----------|
| **File format** | Markdown (`.md`) with fenced code blocks for snippets |
| **Naming** | Kebab-case filenames; Title-case H1s |
| **Commit messages** | `feat(auth): add OAuth callback – see docs/auth/log.md#2025-04-25` |
| **AI agents** | Provide path to doc and explicit “update-this-doc” instruction in *every* prompt |
| **Linking code ↔ docs** | Use inline comment: `// DOC: docs/stripe/spec.md#webhooks` |
| **Searchability** | Headers ≤ H3; never bury critical info in paragraphs |

---

#### 5. Example Prompt to AI Assistant  

```text
You are the dev agent for LuaPer Tech.  
Task: Integrate Stripe subscriptions.  
Context: See docs/payments-stripe/*.md.  
Steps:  
1. Read brief.md and spec.md to understand goals and constraints.  
2. Implement backend endpoints as per spec.  
3. Update docs/payments-stripe/log.md with:  
   - Date, short description, commit hash  
   - Any new env vars or config keys  
4. Regenerate spec.md sections affected (e.g., API examples).  
Return only the commit diff summary and updated docs.
```

---

#### 6. Quality Gate Checklist (CI)  

- ✅ **Lint Docs** – no broken links, invalid headers, or TODOs.  
- ✅ **Validate ADR Index** – ADR numbers sequential.  
- ✅ **Doc Coverage** – Every merged PR must touch at least one doc file.  
- ✅ **Automated Summary Bot** – Posts a nightly diff of `/docs` updates to `#dev-updates`.

---

### 📌 Next Steps for the Team  

1. **Adopt templates** – clone `docs/_templates/*` now.  
2. **Retrofit current features** – allocate 2 hours per feature to backfill briefs & logs.  
3. **Update your AI prompts** – always start with *“Consult the docs…”* and end with *“Update the docs…”*.  
4. **Enforce via PR reviews** – decline any PR missing the required doc updates.  

When we all treat documentation as first-class code, our AI helpers stay grounded, our velocity doubles, and our product quality climbs. Let’s make this the default way we work.