#!/usr/bin/env python3
"""Simple HTTP server for testing the frontend"""

import http.server
import socketserver
import os
import mimetypes

PORT = 8080
DIRECTORY = "."

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
        # Add JavaScript MIME type
        mimetypes.add_type('application/javascript', '.js')
        mimetypes.add_type('text/css', '.css')

    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_GET(self):
        original_path = self.path
        print(f"Request: {original_path}")

        # Proxy API requests to the backend
        if self.path.startswith('/api/'):
            import urllib.request
            import urllib.error
            try:
                # Forward request to API backend
                api_url = f"http://localhost:8000{self.path}"
                with urllib.request.urlopen(api_url) as response:
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    self.end_headers()
                    self.wfile.write(response.read())
                return
            except urllib.error.URLError as e:
                print(f"API proxy error: {e}")
                self.send_error(503, "API backend not available")
                return

        # Only redirect to index.html for routes that don't have file extensions
        # and are not in static directories
        should_redirect = (
            self.path != '/' and
            not self.path.startswith('/src/') and
            not self.path.startswith('/public/') and
            not self.path.startswith('/static/') and
            not self.path.endswith('.html') and
            not self.path.endswith('.js') and
            not self.path.endswith('.css') and
            not self.path.endswith('.png') and
            not self.path.endswith('.jpg') and
            not self.path.endswith('.svg') and
            not self.path.endswith('.ico')
        )

        if should_redirect:
            print(f"Redirecting {original_path} to /index.html")
            self.path = '/index.html'
        else:
            print(f"Serving static file: {original_path}")

        return super().do_GET()

os.chdir(os.path.dirname(os.path.abspath(__file__)))

with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
    print(f"Server running at http://localhost:{PORT}/")
    print("Press Ctrl+C to stop")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")