# TargetWise Terminal Notifications

A comprehensive terminal notification system for the TargetWise development workflow.

## Features

- 🎨 Colorful, consistent terminal output
- 📊 Built-in support for different notification types (success, error, warning, etc.)
- 🔍 Context-aware with automatic timestamping
- 🤖 CI-friendly (reduces noise in CI environments)
- 📝 Git integration (shows current branch)

## Installation

Dependencies are automatically installed when you run `npm install`.

## Usage

### Basic Usage

```javascript
const notify = require('./notify');

// Basic notifications
notify.info('Starting application');
notify.success('Operation completed successfully');
notify.warning('This is a warning');
notify.error('Something went wrong!');

// With context
notify.info('User logged in', { context: 'Auth' });

// Build and test notifications
notify.build('Building application...');
notify.testResults({ passed: 10, failed: 2, skipped: 1, duration: 2500 });

// Audit results
notify.auditResults({
  vulnerabilities: {
    critical: 2,
    high: 3,
    moderate: 1,
    low: 0
  }
});
```

### Available Notification Types

- `notify.success(message, options)` - Success notification
- `notify.error(message, options)` - Error notification
- `notify.warning(message, options)` - Warning notification
- `notify.info(message, options)` - Info notification
- `notify.debug(message, options)` - Debug information
- `notify.build(message, options)` - Build-related notifications
- `notify.test(message, options)` - Test-related notifications
- `notify.audit(message, options)` - Audit-related notifications
- `notify.git(message, options)` - Git-related notifications (shows branch by default)
- `notify.resource(message, options)` - Resource usage notifications

### Options

All notification methods accept an optional options object with the following properties:

- `context` (string): Additional context for the message
- `showTimestamp` (boolean, default: true): Whether to show the timestamp
- `showBranch` (boolean, default: false for most, true for git): Whether to show the git branch
- `skipInCI` (boolean, default: true): Whether to skip this notification in CI environments

### Demo

Run the demo script to see all notification types in action:

```bash
node scripts/demo-notifications.js
```

## Integration with Build Tools

### Webpack

Add to your webpack.config.js:

```javascript
const notify = require('./scripts/notify');

module.exports = {
  // ... other config
  plugins: [
    {
      apply: (compiler) => {
        compiler.hooks.done.tap('DonePlugin', (stats) => {
          if (stats.hasErrors()) {
            notify.error('Build failed with errors');
          } else {
            notify.buildComplete(stats.endTime - stats.startTime);
          }
        });
      },
    },
  ],
};
```

### Jest

Add to your Jest config:

```javascript
const notify = require('./scripts/notify');

module.exports = {
  // ... other config
  reporters: [
    'default',
    [
      './scripts/notify-reporter.js',
      {
        onTestResults: (testResults) => {
          notify.testResults({
            passed: testResults.numPassedTests,
            failed: testResults.numFailedTests,
            skipped: testResults.numPendingTests,
            duration: testResults.testResults.reduce(
              (sum, suite) => sum + (suite.endTime - suite.startTime),
              0
            ),
          });
        },
      },
    ],
  ],
};
```

## Best Practices

1. Use appropriate notification types (don't use error for warnings)
2. Include relevant context in your messages
3. Keep messages concise but informative
4. Use the `skipInCI` option for non-critical notifications in CI environments
5. For long-running operations, consider using spinners (via the `ora` package)

## License

MIT
