<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise Test Page</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 40px;
            background: #f9fafb;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 32px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e293b;
            margin-bottom: 24px;
        }
        .status {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
        }
        .test-links {
            display: grid;
            gap: 12px;
            margin-top: 24px;
        }
        .test-link {
            display: block;
            padding: 12px 20px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            transition: background 0.2s;
        }
        .test-link:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 TargetWise Application Test</h1>
        
        <div class="status info">
            <strong>Server Status:</strong> Running on http://localhost:8080/
        </div>
        
        <div id="js-status" class="status">
            <strong>JavaScript Status:</strong> Checking...
        </div>
        
        <div id="css-status" class="status">
            <strong>CSS Status:</strong> Checking...
        </div>
        
        <h2>Test Links</h2>
        <div class="test-links">
            <a href="/" class="test-link">Main Application</a>
            <a href="/demo.html" class="test-link">Demo Page</a>
            <a href="/dashboard" class="test-link">Dashboard</a>
            <a href="/search" class="test-link">Search</a>
            <a href="/pool" class="test-link">Interest Pool</a>
            <a href="/suggestions" class="test-link">Suggestions</a>
        </div>
        
        <div id="app-test" style="margin-top: 24px;"></div>
    </div>
    
    <script>
        // Test JavaScript loading
        document.getElementById('js-status').className = 'status success';
        document.getElementById('js-status').innerHTML = '<strong>JavaScript Status:</strong> ✓ Working';
        
        // Test CSS loading
        fetch('/src/styles/index.css')
            .then(response => {
                if (response.ok) {
                    document.getElementById('css-status').className = 'status success';
                    document.getElementById('css-status').innerHTML = '<strong>CSS Status:</strong> ✓ Loaded successfully';
                } else {
                    throw new Error('CSS not found');
                }
            })
            .catch(error => {
                document.getElementById('css-status').className = 'status error';
                document.getElementById('css-status').innerHTML = '<strong>CSS Status:</strong> ✗ ' + error.message;
            });
        
        // Test app.js module loading
        import('/src/app.js')
            .then(module => {
                document.getElementById('app-test').innerHTML = '<div class="status success"><strong>App Module:</strong> ✓ Loaded successfully</div>';
            })
            .catch(error => {
                document.getElementById('app-test').innerHTML = '<div class="status error"><strong>App Module:</strong> ✗ ' + error.message + '</div>';
                console.error('Module loading error:', error);
            });
    </script>
</body>
</html>