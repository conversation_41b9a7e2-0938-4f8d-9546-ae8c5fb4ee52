<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interest Pool - TargetWise</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f0f2f5;
            color: #1a1a2e;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2563eb;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .logo:hover {
            transform: translateY(-1px);
        }

        .logo svg {
            width: 32px;
            height: 32px;
        }

        .nav-menu {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .nav-link {
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .nav-link:hover {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.08);
        }

        .nav-link.active {
            color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .theme-toggle {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: none;
            background: #f1f5f9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .theme-toggle:hover {
            background: #e2e8f0;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline {
            background: transparent;
            color: #64748b;
            border: 2px solid #e2e8f0;
        }

        .btn-outline:hover {
            border-color: #cbd5e1;
            background: #f8fafc;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
            box-shadow: 0 4px 14px rgba(37, 99, 235, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.35);
        }

        /* Main Layout */
        .main-container {
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            gap: 24px;
            padding: 24px;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 10px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: all 0.2s;
        }

        .sidebar-item:hover {
            background: #f1f5f9;
            color: #1e293b;
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            color: #2563eb;
        }

        .sidebar-icon {
            width: 20px;
            height: 20px;
            opacity: 0.7;
        }

        /* Content Area */
        .content {
            flex: 1;
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .page-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 16px;
            margin-left: 64px;
        }

        .page-actions {
            display: flex;
            gap: 12px;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 24px;
        }

        .stat-card:nth-child(2) .stat-icon {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .stat-card:nth-child(3) .stat-icon {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }

        .stat-card:nth-child(4) .stat-icon {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 14px;
        }

        /* Collection Info */
        .collection-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #3b82f6;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .info-icon {
            width: 40px;
            height: 40px;
            background: #2563eb;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .info-text {
            color: #1e40af;
            line-height: 1.6;
            flex: 1;
        }

        /* Actions Bar */
        .actions-bar {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
            color: white;
        }

        .selection-info {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #64748b;
        }

        /* Filters */
        .filters-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .filters-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
            gap: 16px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
        }

        .filter-input {
            padding: 10px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
        }

        .filter-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .range-inputs {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .range-input {
            flex: 1;
        }

        .range-separator {
            color: #94a3b8;
        }

        .btn-clear-filters {
            background: white;
            color: #8b5cf6;
            border: 2px solid #8b5cf6;
        }

        .btn-clear-filters:hover {
            background: rgba(139, 92, 246, 0.05);
        }

        /* Table */
        .table-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        .table-header {
            padding: 24px 24px 16px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            text-align: left;
            padding: 16px 24px;
            font-weight: 600;
            color: #475569;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: #fafbfc;
            border-bottom: 2px solid #f1f5f9;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
        }

        tr:hover {
            background: #fafbfc;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
        }

        .checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
            accent-color: #2563eb;
        }

        .interest-id {
            font-family: monospace;
            color: #64748b;
            font-size: 13px;
        }

        .interest-name {
            font-weight: 500;
            color: #1e293b;
        }

        .audience-badge {
            display: inline-block;
            padding: 4px 12px;
            background: #e0f2fe;
            color: #0369a1;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        .interest-type {
            display: inline-block;
            padding: 4px 12px;
            background: #f3e8ff;
            color: #7c3aed;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }

        .path-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #64748b;
            font-size: 13px;
        }

        .path-separator {
            color: #cbd5e1;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            background: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            color: #64748b;
        }

        .action-btn:hover {
            border-color: #2563eb;
            color: #2563eb;
            background: rgba(37, 99, 235, 0.05);
        }

        .action-btn.delete-btn:hover {
            border-color: #ef4444;
            color: #ef4444;
            background: rgba(239, 68, 68, 0.05);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
        }

        .empty-icon {
            width: 100px;
            height: 100px;
            background: #f1f5f9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 48px;
        }

        .empty-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .empty-text {
            color: #64748b;
            margin-bottom: 24px;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            
            .main-container {
                padding: 16px;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 640px) {
            .header-content {
                padding: 12px 16px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .page-header {
                padding: 24px 16px;
            }
            
            .page-title {
                font-size: 24px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                width: 100%;
            }
            
            .btn {
                flex: 1;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="#" class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <circle cx="12" cy="12" r="6"></circle>
                    <circle cx="12" cy="12" r="2"></circle>
                </svg>
                TargetWise
            </a>
            
            <nav class="nav-menu">
                <a href="#" class="nav-link">Home</a>
                <a href="#" class="nav-link active">Micro-Tools</a>
                <a href="#" class="nav-link">Documentation</a>
                <a href="#" class="nav-link">Pricing</a>
                <a href="#" class="nav-link">Admin</a>
            </nav>
            
            <div class="header-actions">
                <button class="theme-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
                <a href="#" class="btn btn-outline">Log In</a>
                <a href="#" class="btn btn-primary">Sign Up Free</a>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3 class="sidebar-title">Micro-Tools</h3>
            <nav class="sidebar-menu">
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    Dashboard
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    Interest Search
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
                    </svg>
                    Interest Suggestions
                </a>
                <a href="#" class="sidebar-item">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 3v18h18"></path>
                        <path d="m19 9-5 5-4-4-3 3"></path>
                    </svg>
                    Taxonomy Browser
                </a>
                <a href="#" class="sidebar-item active">
                    <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
                    </svg>
                    Interest Pool
                </a>
            </nav>
        </aside>

        <!-- Content -->
        <main class="content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-header-top">
                    <div>
                        <h1 class="page-title">
                            <div class="page-icon">📁</div>
                            Interest Pool
                        </h1>
                        <p class="page-subtitle">Manage your collected interests</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 18l6-6-6-6"></path>
                            </svg>
                            Back to Tools
                        </button>
                        <button class="btn btn-primary">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                                <line x1="4" y1="22" x2="4" y2="15"></line>
                            </svg>
                            Main App
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-value">2</div>
                    <div class="stat-label">Total Interests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-value">0</div>
                    <div class="stat-label">Total Audience</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏷️</div>
                    <div class="stat-value">1</div>
                    <div class="stat-label">Interest Types</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📁</div>
                    <div class="stat-value">2</div>
                    <div class="stat-label">Categories</div>
                </div>
            </div>

            <!-- Collection Info -->
            <div class="collection-info">
                <div class="info-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                    </svg>
                </div>
                <div class="info-text">
                    This page shows all interests you've collected from the micro-tools. You can export them to CSV or send them to the main application for use in your Facebook ad campaigns.
                </div>
            </div>

            <!-- Actions Bar -->
            <div class="actions-bar">
                <div class="action-buttons">
                    <button class="btn btn-success">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        Export to CSV
                    </button>
                    <button class="btn btn-secondary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                        Clear All
                    </button>
                    <button class="btn btn-primary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                        Send to Main App
                    </button>
                </div>
                <div class="selection-info">
                    <label style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" id="selectAll" class="checkbox">
                        <span>Select All</span>
                    </label>
                    <span id="selectionCount">0 selected</span>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-header">
                    <h3 class="filters-title">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                        </svg>
                        Filters
                    </h3>
                    <button class="btn btn-clear-filters">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                        Clear Filters
                    </button>
                </div>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label">Name</label>
                        <input type="text" class="filter-input" placeholder="Filter by name...">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Audience Size</label>
                        <div class="range-inputs">
                            <input type="text" class="filter-input range-input" placeholder="Min">
                            <span class="range-separator">to</span>
                            <input type="text" class="filter-input range-input" placeholder="Max">
                        </div>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Type</label>
                        <select class="filter-input">
                            <option>All Types</option>
                            <option>Interests</option>
                            <option>Behaviors</option>
                            <option>Demographics</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">Path</label>
                        <input type="text" class="filter-input" placeholder="Filter by path...">
                    </div>
                </div>
            </div>

            <!-- Table -->
            <div class="table-container">
                <div class="table-header">
                    <h3 class="table-title">Collected Interests</h3>
                    <span class="stat-label">2 items</span>
                </div>
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th style="width: 50px;">
                                    <input type="checkbox" class="checkbox" id="headerCheckbox">
                                </th>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Audience Size</th>
                                <th>Type</th>
                                <th>Path</th>
                                <th style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" class="checkbox row-checkbox">
                                    </div>
                                </td>
                                <td class="interest-id">6003288647527</td>
                                <td class="interest-name">Projectors (consumer electronics)</td>
                                <td>
                                    <span class="audience-badge">N/A</span>
                                </td>
                                <td>
                                    <span class="interest-type">interests</span>
                                </td>
                                <td>
                                    <div class="path-breadcrumb">
                                        <span>Interests</span>
                                        <span class="path-separator">›</span>
                                        <span>Technology</span>
                                        <span class="path-separator">›</span>
                                        <span>Consumer electronics</span>
                                        <span class="path-separator">›</span>
                                        <span>Projectors</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="Copy ID">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                        <button class="action-btn delete-btn" title="Remove">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3 6 5 6 21 6"></polyline>
                                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" class="checkbox row-checkbox">
                                    </div>
                                </td>
                                <td class="interest-id">6002839798079</td>
                                <td class="interest-name">Slide projector (consumer electronics)</td>
                                <td>
                                    <span class="audience-badge">N/A</span>
                                </td>
                                <td>
                                    <span class="interest-type">interests</span>
                                </td>
                                <td>
                                    <div class="path-breadcrumb">
                                        <span>Interests</span>
                                        <span class="path-separator">›</span>
                                        <span>Additional Interests</span>
                                        <span class="path-separator">›</span>
                                        <span>Slide projector</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="Copy ID">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                        <button class="action-btn delete-btn" title="Remove">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3 6 5 6 21 6"></polyline>
                                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Select All functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const headerCheckbox = document.getElementById('headerCheckbox');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const selectionCount = document.getElementById('selectionCount');

        function updateSelectionCount() {
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
            selectionCount.textContent = `${checkedCount} selected`;
        }

        selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            headerCheckbox.checked = this.checked;
            updateSelectionCount();
        });

        headerCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            selectAllCheckbox.checked = this.checked;
            updateSelectionCount();
        });

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectionCount);
        });

        // Copy to clipboard
        document.querySelectorAll('.action-btn:not(.delete-btn)').forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.closest('tr').querySelector('.interest-id').textContent;
                navigator.clipboard.writeText(id);
                
                // Visual feedback
                const originalHTML = this.innerHTML;
                this.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20 6 9 17 4 12"></polyline></svg>';
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                }, 1000);
            });
        });

        // Delete functionality
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('tr');
                row.style.transform = 'translateX(-100%)';
                row.style.opacity = '0';
                setTimeout(() => {
                    row.remove();
                    updateSelectionCount();
                    
                    // Update stats
                    const totalItems = document.querySelectorAll('tbody tr').length;
                    document.querySelector('.table-header .stat-label').textContent = `${totalItems} items`;
                    document.querySelector('.stat-card:first-child .stat-value').textContent = totalItems;
                }, 300);
            });
        });

        // Filter functionality
        const nameFilter = document.querySelector('.filter-input[placeholder="Filter by name..."]');
        const pathFilter = document.querySelector('.filter-input[placeholder="Filter by path..."]');

        function applyFilters() {
            const nameValue = nameFilter.value.toLowerCase();
            const pathValue = pathFilter.value.toLowerCase();
            
            document.querySelectorAll('tbody tr').forEach(row => {
                const name = row.querySelector('.interest-name').textContent.toLowerCase();
                const path = row.querySelector('.path-breadcrumb').textContent.toLowerCase();
                
                const nameMatch = name.includes(nameValue);
                const pathMatch = path.includes(pathValue);
                
                row.style.display = nameMatch && pathMatch ? '' : 'none';
            });
        }

        nameFilter.addEventListener('input', applyFilters);
        pathFilter.addEventListener('input', applyFilters);

        // Clear filters
        document.querySelector('.btn-clear-filters').addEventListener('click', function() {
            document.querySelectorAll('.filter-input').forEach(input => {
                input.value = '';
            });
            applyFilters();
        });

        // Export to CSV
        document.querySelector('.btn-success').addEventListener('click', function() {
            // Simulate export
            this.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20 6 9 17 4 12"></polyline></svg> Exported!';
            setTimeout(() => {
                this.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg> Export to CSV';
            }, 2000);
        });
    </script>
</body>
</html>