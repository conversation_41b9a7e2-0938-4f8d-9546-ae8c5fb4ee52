# TargetWise Project Structure

## Overview
TargetWise is now organized into two main components:
- **API**: Backend REST API built with FastAPI
- **Frontend**: Modern web application built with Vite

## Directory Structure

```
TargetWise/
├── api/                      # Backend API
│   ├── api/                  # API routes
│   │   └── v1/              # API version 1
│   ├── models/              # Data models
│   ├── schemas/             # Pydantic schemas
│   ├── services/            # Business logic
│   ├── templates/           # Admin UI templates
│   └── utils/               # Utility functions
│
├── frontend/                 # Frontend application
│   ├── public/              # Static assets
│   ├── src/                 # Source code
│   │   ├── components/      # React components
│   │   ├── hooks/           # Custom hooks
│   │   ├── pages/           # Page components
│   │   ├── services/        # API services
│   │   └── styles/          # CSS styles
│   ├── tests/               # Frontend tests
│   └── package.json         # Frontend dependencies
│
├── config/                   # Configuration files
├── docs/                     # Documentation
├── infrastructure/           # Infrastructure code
├── logs/                     # Application logs
├── requirements/             # Python dependencies
├── tests/                    # Backend tests
│
├── .github/                  # GitHub workflows
├── .husky/                   # Git hooks
├── pyproject.toml           # Python project config
├── run.py                   # API entry point
└── README.md                # Project documentation
```

## Running the Application

### Backend API
```bash
# Install dependencies
pip install -r requirements/development.txt

# Run the API
python run.py
```

### Frontend
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build
```

## Key Features
- RESTful API for Facebook Marketing integrations
- Modern React frontend with Vite
- Admin dashboard for configuration
- Real-time notifications
- Comprehensive logging system