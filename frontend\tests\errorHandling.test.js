/**
 * @fileoverview Error handling tests for micro-tools
 * @jest-environment jsdom
 */

describe('Error Handling', () => {
  test('should display error notification', () => {
    // Create a mock notification element
    document.body.innerHTML = '<div id="notifications"></div>';
    
    // Mock error handler
    const errorHandler = {
      showError: jest.fn(),
      showWarning: jest.fn(),
      showSuccess: jest.fn()
    };
    
    // Test error display
    errorHandler.showError('Test error message');
    expect(errorHandler.showError).toHaveBeenCalledWith('Test error message');
  });

  test('should handle API errors gracefully', () => {
    const apiError = {
      status: 500,
      message: 'Internal Server Error'
    };
    
    const errorHandler = {
      handleApiError: jest.fn((error) => {
        return {
          userMessage: 'Something went wrong. Please try again.',
          shouldRetry: error.status >= 500
        };
      })
    };
    
    const result = errorHandler.handleApiError(apiError);
    expect(result.userMessage).toBe('Something went wrong. Please try again.');
    expect(result.shouldRetry).toBe(true);
  });

  test('should validate input and show field errors', () => {
    const validator = {
      validateSearchInput: jest.fn((input) => {
        if (!input || input.trim().length === 0) {
          return { valid: false, error: 'Search term is required' };
        }
        if (input.length < 2) {
          return { valid: false, error: 'Search term must be at least 2 characters' };
        }
        return { valid: true };
      })
    };
    
    expect(validator.validateSearchInput('')).toEqual({
      valid: false,
      error: 'Search term is required'
    });
    
    expect(validator.validateSearchInput('a')).toEqual({
      valid: false, 
      error: 'Search term must be at least 2 characters'
    });
    
    expect(validator.validateSearchInput('valid input')).toEqual({
      valid: true
    });
  });
});