/**
 * Terminal Notification Utility for TargetWise
 * Provides colorful, consistent terminal notifications for development workflows
 */

// Use a simple color implementation for maximum compatibility
const colors = {
  // Text colors
  black: str => `\x1b[30m${str}\x1b[0m`,
  red: str => `\x1b[31m${str}\x1b[0m`,
  green: str => `\x1b[32m${str}\x1b[0m`,
  yellow: str => `\x1b[33m${str}\x1b[0m`,
  blue: str => `\x1b[34m${str}\x1b[0m`,
  magenta: str => `\x1b[35m${str}\x1b[0m`,
  cyan: str => `\x1b[36m${str}\x1b[0m`,
  white: str => `\x1b[37m${str}\x1b[0m`,
  gray: str => `\x1b[90m${str}\x1b[0m`,
  
  // Background colors
  bgBlack: str => `\x1b[40m${str}\x1b[49m`,
  bgRed: str => `\x1b[41m${str}\x1b[49m`,
  bgGreen: str => `\x1b[42m${str}\x1b[49m`,
  bgYellow: str => `\x1b[43m${str}\x1b[49m`,
  bgBlue: str => `\x1b[44m${str}\x1b[49m`,
  bgMagenta: str => `\x1b[45m${str}\x1b[49m`,
  bgCyan: str => `\x1b[46m${str}\x1b[49m`,
  bgWhite: str => `\x1b[47m${str}\x1b[49m`,
  
  // Text styles
  bold: str => `\x1b[1m${str}\x1b[22m`,
  dim: str => `\x1b[2m${str}\x1b[22m`,
  italic: str => `\x1b[3m${str}\x1b[23m`,
  underline: str => `\x1b[4m${str}\x1b[24m`,
  inverse: str => `\x1b[7m${str}\x1b[27m`,
  hidden: str => `\x1b[8m${str}\x1b[28m`,
  strikethrough: str => `\x1b[9m${str}\x1b[29m`
};

const { execSync } = require('child_process');

// Check if we're running in a CI environment
const isCI = process.env.CI === 'true' || process.env.CI === '1';

// Notification types with styles
const types = {
  success: {
    color: 'green',
    icon: '✅',
    label: 'SUCCESS'
  },
  error: {
    color: 'red',
    icon: '❌',
    label: 'ERROR'
  },
  warning: {
    color: 'yellow',
    icon: '⚠️',
    label: 'WARNING'
  },
  info: {
    color: 'blue',
    icon: 'ℹ️',
    label: 'INFO'
  },
  debug: {
    color: 'gray',
    icon: '🐛',
    label: 'DEBUG'
  },
  build: {
    color: 'cyan',
    icon: '🔨',
    label: 'BUILD'
  },
  test: {
    color: 'magenta',
    icon: '🧪',
    label: 'TEST'
  },
  audit: {
    color: 'yellow',
    icon: '🔍',
    label: 'AUDIT'
  },
  git: {
    color: 'white',
    icon: '🔀',
    label: 'GIT'
  },
  resource: {
    color: 'cyan',
    icon: '📊',
    label: 'RESOURCE'
  }
};

/**
 * Format a timestamp for logging
 * @returns {string} Formatted timestamp
 */
function getTimestamp() {
  return new Date().toISOString();
}

/**
 * Get the current git branch
 * @returns {string} Current git branch name
 */
function getGitBranch() {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', { stdio: 'pipe' })
      .toString()
      .trim();
  } catch (e) {
    return 'unknown';
  }
}

/**
 * Create a notification message
 * @param {string} type - Notification type (success, error, warning, etc.)
 * @param {string} message - The message to display
 * @param {Object} [options] - Additional options
 * @param {string} [options.context] - Additional context for the message
 * @param {boolean} [options.showTimestamp=true] - Whether to show timestamp
 * @param {boolean} [options.showBranch=false] - Whether to show git branch
 * @returns {string} Formatted notification message
 */
function createNotification(type, message, options = {}) {
  const {
    context = '',
    showTimestamp = true,
    showBranch = false,
  } = options;

  const notificationType = types[type] || types.info;
  const timestamp = showTimestamp ? colors.gray(`[${getTimestamp()}] `) : '';
  const branch = showBranch ? colors.magenta(`[${getGitBranch()}] `) : '';
  const contextStr = context ? colors.blue(`[${context}] `) : '';
  const colorFn = colors[notificationType.color] || ((s) => s);
  const label = colors.bold(colorFn(`[${notificationType.label}]`));
  
  return `${timestamp}${branch}${label} ${notificationType.icon}  ${contextStr}${message}`;
}

/**
 * Display a notification in the terminal
 * @param {string} type - Notification type
 * @param {string} message - The message to display
 * @param {Object} [options] - Additional options
 */
function notify(type, message, options = {}) {
  if (isCI && options.skipInCI !== false) {
    // In CI, only show errors and warnings by default
    if (type !== 'error' && type !== 'warning') {
      return;
    }
  }
  
  const notification = createNotification(type, message, options);
  console.log(notification);
}

// Helper methods for common notification types
const notifyHelpers = {
  success: (message, options) => notify('success', message, options),
  error: (message, options) => notify('error', message, options),
  warning: (message, options) => notify('warning', message, options),
  info: (message, options) => notify('info', message, options),
  debug: (message, options) => notify('debug', message, options),
  build: (message, options) => notify('build', message, options),
  test: (message, options) => notify('test', message, options),
  audit: (message, options) => notify('audit', message, options),
  git: (message, options) => notify('git', message, { ...options, showBranch: true }),
  resource: (message, options) => notify('resource', message, options),
  
  // Specialized notifications
  buildComplete: (duration) => {
    const time = (duration / 1000).toFixed(2);
    notify('success', `Build completed in ${time}s`, { skipInCI: false });
  },
  
  testResults: ({ passed, failed, skipped, duration }) => {
    const time = (duration / 1000).toFixed(2);
    const message = [
      `Tests: ${colors.green(`${passed} passed`)},`,
      failed ? colors.red(`${failed} failed`) : null,
      skipped ? colors.yellow(`${skipped} skipped`) : null,
      `in ${time}s`
    ].filter(Boolean).join(' ');
    
    notify('test', message, { skipInCI: false });
  },
  
  auditResults: ({ vulnerabilities }) => {
    const { critical, high, moderate, low } = vulnerabilities;
    const issues = [
      critical ? colors.red(`${critical} critical`) : null,
      high ? colors.magenta(`${high} high`) : null,
      moderate ? colors.yellow(`${moderate} moderate`) : null,
      low ? colors.blue(`${low} low`) : null
    ].filter(Boolean).join(', ');
    
    if (critical || high) {
      notify('error', `Security vulnerabilities found: ${issues}`, { skipInCI: false });
    } else if (moderate || low) {
      notify('warning', `Security vulnerabilities found: ${issues}`, { skipInCI: false });
    } else {
      notify('success', 'No security vulnerabilities found', { skipInCI: false });
    }
  },
  
  resourceUsage: ({ memory, cpu }) => {
    const memoryUsage = Math.round(memory.used / 1024 / 1024);
    const memoryTotal = Math.round(memory.total / 1024 / 1024);
    const memoryPercent = Math.round((memory.used / memory.total) * 100);
    
    let memoryMessage = `Memory: ${memoryUsage}MB/${memoryTotal}MB (${memoryPercent}%)`;
    if (memoryPercent > 90) {
      memoryMessage = colors.red(memoryMessage);
    } else if (memoryPercent > 70) {
      memoryMessage = colors.yellow(memoryMessage);
    }
    
    notify('resource', memoryMessage, { context: 'System' });
    
    if (cpu) {
      const cpuMessage = `CPU: ${Math.round(cpu.usage * 100)}%`;
      notify('resource', cpuMessage, { context: 'System' });
    }
  }
};

module.exports = {
  ...notifyHelpers,
  // Export the base notify function for custom notifications
  notify,
  // For testing
  _types: types,
  _createNotification: createNotification
};
