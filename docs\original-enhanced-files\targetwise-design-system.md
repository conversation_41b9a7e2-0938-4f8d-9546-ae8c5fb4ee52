# TargetWise Design System

## 🎨 Design Philosophy

The TargetWise design system is built on principles of **modern minimalism**, **clarity**, and **professional aesthetics**. It emphasizes:

- **Clean, spacious layouts** with generous white space
- **Subtle depth** through shadows and layering
- **Smooth interactions** with thoughtful animations
- **Accessibility** and responsive design
- **Consistency** across all micro-tools

---

## 🔤 Typography

### Font Stack
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
```
This system font stack ensures optimal readability across all platforms while maintaining a native feel.

### Type Scale
- **Display**: 36px (700 weight) - Page titles
- **Heading 1**: 32px (700 weight) - Main headings
- **Heading 2**: 28px (700 weight) - Section headings
- **Heading 3**: 20px (600 weight) - Card titles
- **Heading 4**: 18px (600 weight) - Subsection titles
- **Body**: 16px (400 weight) - Default text
- **Small**: 14px (400/500 weight) - Secondary text
- **Caption**: 13px (400 weight) - Meta information

### Font Weights
- **Regular**: 400 - Body text
- **Medium**: 500 - Navigation, buttons
- **Semibold**: 600 - Subheadings, emphasis
- **Bold**: 700 - Main headings

---

## 🎨 Color Palette

### Primary Colors
- **Primary Blue**: `#2563eb` - Main brand color
- **Primary Gradient**: `linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)`
- **Primary Hover**: `#1d4ed8`
- **Primary Light**: `rgba(37, 99, 235, 0.1)`

### Secondary Colors
- **Success Green**: `#10b981` / Gradient: `linear-gradient(135deg, #10b981 0%, #34d399 100%)`
- **Warning Amber**: `#f59e0b` / Gradient: `linear-gradient(135deg, #f59e0b 0%, #f97316 100%)`
- **Danger Red**: `#ef4444` / Gradient: `linear-gradient(135deg, #ef4444 0%, #f87171 100%)`
- **Purple Accent**: `#8b5cf6` / Gradient: `linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%)`

### Neutral Colors
- **Gray 900**: `#1a1a2e` - Primary text
- **Gray 800**: `#1e293b` - Headings
- **Gray 700**: `#334155` - Strong text
- **Gray 600**: `#475569` - Medium emphasis
- **Gray 500**: `#64748b` - Body text
- **Gray 400**: `#94a3b8` - Muted text
- **Gray 300**: `#cbd5e1` - Borders
- **Gray 200**: `#e2e8f0` - Light borders
- **Gray 100**: `#f1f5f9` - Backgrounds
- **Gray 50**: `#f8fafc` - Light backgrounds
- **White**: `#ffffff` - Base background

### Background Gradients
```css
/* Icon backgrounds */
Blue: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)
Green: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)
Yellow: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)
Pink: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%)
Purple: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%)
```

---

## 📐 Spacing System

Based on an 8px grid system:
- **4px**: Tight spacing (inside components)
- **8px**: Default small spacing
- **12px**: Medium spacing
- **16px**: Standard spacing
- **20px**: Large spacing
- **24px**: Extra large spacing
- **32px**: Section spacing
- **48px**: Major section spacing

---

## 🔲 Layout System

### Container Widths
- **Max Width**: 1400px
- **Content Max Width**: 1200px
- **Sidebar Width**: 260px
- **Min Card Width**: 300px

### Grid System
- **Dashboard Grid**: `repeat(auto-fit, minmax(300px, 1fr))`
- **Stats Grid**: `repeat(auto-fit, minmax(250px, 1fr))`
- **Two Column**: `1fr 1fr` (responsive to single column)

### Breakpoints
- **Mobile**: < 640px
- **Tablet**: < 1024px
- **Desktop**: ≥ 1024px

---

## 🎯 Component Design

### Border Radius
- **Small**: 6px - Buttons, badges
- **Medium**: 8px - Input fields, small cards
- **Large**: 10px - Dropdown containers
- **Extra Large**: 12px - Cards, modals
- **XXL**: 16px - Major containers
- **Round**: 20px - Pills, badges
- **Circle**: 50% - Avatars, round buttons

### Shadows
```css
/* Elevation scale */
Shadow XS: 0 1px 2px rgba(0, 0, 0, 0.05)
Shadow SM: 0 2px 8px rgba(0, 0, 0, 0.04)
Shadow MD: 0 4px 14px rgba(0, 0, 0, 0.08)
Shadow LG: 0 8px 24px rgba(0, 0, 0, 0.12)
Shadow XL: 0 12px 32px rgba(0, 0, 0, 0.16)

/* Colored shadows */
Primary: 0 4px 14px rgba(37, 99, 235, 0.25)
Primary Hover: 0 6px 20px rgba(37, 99, 235, 0.35)
```

### Transitions
```css
/* Standard timing */
transition: all 0.2s ease;

/* Animation curves */
ease-out: cubic-bezier(0.4, 0, 0.2, 1)
ease-in-out: cubic-bezier(0.4, 0, 1, 1)

/* Durations */
Fast: 0.15s
Normal: 0.2s
Slow: 0.3s
```

---

## 🧩 Component Patterns

### Buttons
1. **Primary**: Gradient background, white text, shadow
2. **Secondary**: Gray background, dark text
3. **Outline**: Transparent with border
4. **Ghost**: Transparent with subtle hover
5. **Icon**: Square aspect ratio, minimal padding

### Cards
- White background
- 16px border radius
- Subtle shadow (0 2px 8px rgba(0, 0, 0, 0.04))
- 32px padding (24px on mobile)
- Hover elevation effect

### Input Fields
- 2px border (#e2e8f0)
- 12px border radius
- 14px vertical, 20px horizontal padding
- Focus state with blue border and shadow
- Placeholder color: #94a3b8

### Navigation
- Sticky header with backdrop blur
- Sidebar with gradient active states
- Smooth hover transitions
- Clear visual hierarchy

### Tables
- Clean borders with #f1f5f9
- Hover state on rows
- Sticky headers
- Monospace font for IDs
- Action buttons in last column

### Badges & Pills
- 20px border radius
- Small padding (4px 12px)
- Colored backgrounds with matching text
- Inline-block display

---

## 🎭 Interaction Patterns

### Hover States
- **Elevation**: Cards lift with increased shadow
- **Color shift**: Buttons darken/lighten
- **Transform**: Slight translations (1-4px)
- **Opacity**: Secondary elements become more prominent

### Active States
- **Gradient backgrounds** for selected items
- **Blue accent** borders
- **Scaled transforms** (0.98x) for press feedback

### Loading States
- **Spinner**: 40px with blue top border
- **Skeleton screens** for content placeholders
- **Smooth transitions** in and out

### Empty States
- Large icon (80-100px)
- Clear messaging
- Gray color scheme (#64748b)
- Call-to-action when appropriate

---

## 📱 Responsive Behavior

### Mobile Optimizations
- Single column layouts
- Hidden sidebars (hamburger menu)
- Stacked buttons
- Reduced padding (16px)
- Simplified navigation

### Tablet Adjustments
- Flexible grids
- Collapsible sidebars
- Touch-friendly tap targets (min 44px)

### Desktop Features
- Multi-column layouts
- Hover interactions
- Sticky sidebars
- Advanced filtering options

---

## ♿ Accessibility

### Color Contrast
- All text meets WCAG AA standards
- Primary buttons have 4.5:1 contrast ratio
- Focus indicators are clearly visible

### Interactive Elements
- Minimum touch target: 44x44px
- Clear focus states
- Keyboard navigation support
- Semantic HTML structure

### Typography
- Minimum font size: 13px
- Line height: 1.6 for body text
- Adequate spacing between elements

---

## 🎬 Animation Guidelines

### Principles
- **Purposeful**: Animations guide attention
- **Fast**: Most transitions under 300ms
- **Smooth**: Use ease-out curve
- **Subtle**: Avoid distracting movements

### Common Animations
```css
/* Fade In */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Slide Down */
@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Spin (loading) */
@keyframes spin {
  to { transform: rotate(360deg); }
}
```

---

## 🔧 Implementation Notes

### CSS Architecture
- Use CSS custom properties for theming
- Mobile-first responsive design
- Modular component structure
- Consistent naming conventions

### Performance
- Optimize animations with `transform` and `opacity`
- Use `will-change` sparingly
- Lazy load heavy components
- Minimize repaints and reflows

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Graceful degradation for older browsers
- System font stack for optimal rendering
- CSS Grid with Flexbox fallbacks

---

This design system ensures consistency, scalability, and a premium user experience across all TargetWise micro-tools while maintaining flexibility for future enhancements.