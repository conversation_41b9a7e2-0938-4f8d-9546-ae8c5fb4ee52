# Contributing to TargetWise

Thank you for your interest in contributing to TargetWise! We appreciate your time and effort in helping us improve this project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Making Changes](#making-changes)
- [Submitting a Pull Request](#submitting-a-pull-request)
- [Reporting Issues](#reporting-issues)
- [Code Style](#code-style)
- [Testing](#testing)
- [Documentation](#documentation)
- [License](#license)

## Code of Conduct

By participating in this project, you are expected to uphold our [Code of Conduct](CODE_OF_CONDUCT.md).

## Getting Started

1. Fork the repository on GitHub.
2. Clone your fork to your local machine.
3. Set up the development environment (see below).

## Development Setup

### Prerequisites

- Python 3.10 or higher
- Node.js 16+ (for frontend development)
- Redis (for caching)
- PostgreSQL (for production, SQLite is used in development)

### Environment Setup

1. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install development dependencies:
   ```bash
   pip install -r requirements/development.txt
   ```

3. Install pre-commit hooks:
   ```bash
   pre-commit install
   ```

4. Copy the example environment file and update it with your credentials:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your Facebook API credentials and other settings.

5. Initialize the database:
   ```bash
   python -m app.db.init_db
   ```

6. Run the development server:
   ```bash
   uvicorn app.main:app --reload
   ```

## Making Changes

1. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b bugfix/issue-number-description
   ```

2. Make your changes following the code style guidelines.

3. Run tests and ensure they pass:
   ```bash
   pytest
   ```

4. Commit your changes with a descriptive commit message:
   ```bash
   git add .
   git commit -m "Add feature: your feature description"
   ```

## Submitting a Pull Request

1. Push your changes to your fork:
   ```bash
   git push origin your-branch-name
   ```

2. Open a pull request against the `main` branch.
3. Fill out the PR template with details about your changes.
4. Request a review from at least one maintainer.

## Reporting Issues

When reporting issues, please include:

- A clear, descriptive title
- Steps to reproduce the issue
- Expected vs. actual behavior
- Any relevant error messages or logs
- Your environment (OS, Python version, etc.)

## Code Style

- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) for Python code.
- Use type hints for all function signatures.
- Keep lines under 88 characters (Black's default).
- Use `isort` for import sorting.
- Use `black` for code formatting.

## Testing

- Write tests for all new features and bug fixes.
- Ensure all tests pass before submitting a PR.
- Use descriptive test function names.
- Follow the Arrange-Act-Assert pattern.

## Documentation

- Update documentation when adding new features or changing behavior.
- Use Google-style docstrings for all public functions and classes.
- Keep the README and other documentation up to date.

## License

By contributing to TargetWise, you agree that your contributions will be licensed under the project's [LICENSE](LICENSE) file.
