// DOC: docs/micro-tools/search-bulk-feature.md
const { test, expect } = require('@playwright/test');

test.describe('Complete Search Flow', () => {
  test('input → results → refinement → saving', async ({ page }) => {
    await page.goto('/micro-tools/search');
    await page.fill('#searchInput', 'Books');
    await page.click('#searchButton');
    await page.waitForSelector('#resultCount:not(:has-text("0"))');
    await page.fill('#searchFilterContainer input[id$="-name-filter"]', 'book');
    await page.waitForTimeout(500);
    const countText = await page.textContent('#resultCount');
    const countNum = parseInt(countText, 10);
    expect(countNum).toBeGreaterThan(0);
    page.on('dialog', dialog => dialog.accept('Books Test'));
    await page.click('#saveSearchButton');
    await expect(page.locator('#savedSearches')).toContainText('Books Test');
  });
});