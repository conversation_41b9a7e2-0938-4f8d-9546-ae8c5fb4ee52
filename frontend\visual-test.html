<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TargetWise - Visual Test</title>
    <link rel="stylesheet" href="/src/styles/targetwise-enhanced.css">
    <style>
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-title {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
        }
        .test-grid {
            display: grid;
            gap: 40px;
        }
        .test-section {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.06);
        }
        .test-section h2 {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }
        .preview-frame {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 16px;
        }
        .page-link {
            display: inline-block;
            padding: 8px 16px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .page-link:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 12px;
        }
        .status-badge.ready {
            background: #10b981;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">TargetWise Visual Test</h1>
            <p style="color: #64748b; font-size: 18px;">
                All pages have been updated to match the exact design from the provided HTML files
            </p>
        </div>

        <div class="test-grid">
            <!-- Header Test -->
            <div class="test-section">
                <h2>Common Header <span class="status-badge ready">Ready</span></h2>
                <div class="preview-frame">
                    <header class="header">
                        <div class="header-content">
                            <a href="/" class="logo">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                                TargetWise
                            </a>
                            
                            <nav class="nav-menu">
                                <a href="/" class="nav-link active">Home</a>
                                <a href="/dashboard" class="nav-link">Dashboard</a>
                                <a href="/search" class="nav-link">Search</a>
                                <a href="/suggestions" class="nav-link">Suggestions</a>
                                <a href="/pool" class="nav-link">Interest Pool</a>
                            </nav>
                            
                            <div class="header-actions">
                                <button class="theme-toggle">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </header>
                </div>
                <p style="color: #64748b;">
                    Responsive header with navigation menu and theme toggle
                </p>
            </div>

            <!-- Hero Section Test -->
            <div class="test-section">
                <h2>Hero Section <span class="status-badge ready">Ready</span></h2>
                <div class="preview-frame" style="height: 300px; overflow: hidden;">
                    <section class="hero-section">
                        <div class="hero-content">
                            <h1 class="hero-title">Build Perfect Targeting Sheets in Seconds</h1>
                            <p class="hero-subtitle">
                                Use our AI-powered algorithm to generate comprehensive 12-column targeting sheets
                            </p>
                            <div class="hero-features">
                                <div class="feature-item">
                                    <div class="feature-icon">✓</div>
                                    <span>AI-Powered Analysis</span>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">✓</div>
                                    <span>12-Column Format</span>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">✓</div>
                                    <span>Instant Results</span>
                                </div>
                            </div>
                        </div>
                        <div class="hero-circle-1"></div>
                        <div class="hero-circle-2"></div>
                    </section>
                </div>
                <p style="color: #64748b;">
                    Gradient hero section with decorative circles
                </p>
            </div>

            <!-- Form Elements Test -->
            <div class="test-section">
                <h2>Form Elements <span class="status-badge ready">Ready</span></h2>
                <div style="max-width: 600px;">
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">📁</div>
                            File Upload
                        </h3>
                        <div class="file-upload-area">
                            <div class="upload-icon">📤</div>
                            <p class="upload-text">Drag & drop your CSV file here</p>
                            <p class="upload-hint">or click to browse</p>
                        </div>
                    </div>

                    <div class="form-section" style="margin-top: 32px;">
                        <h3 class="section-title">
                            <div class="section-icon">✏️</div>
                            Text Input
                        </h3>
                        <div class="textarea-wrapper">
                            <textarea class="form-textarea" placeholder="Enter seed interests..."></textarea>
                            <span class="char-counter">0 / 500</span>
                        </div>
                    </div>

                    <div class="form-section" style="margin-top: 32px;">
                        <button class="btn-submit">
                            Generate Targeting Sheet
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <p style="color: #64748b; margin-top: 24px;">
                    Form elements with proper styling and interactions
                </p>
            </div>

            <!-- Pages Overview -->
            <div class="test-section">
                <h2>All Pages <span class="status-badge ready">Ready</span></h2>
                <div style="display: grid; gap: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                        <div>
                            <strong>TargetWise Main</strong>
                            <p style="color: #64748b; font-size: 14px; margin-top: 4px;">Landing page with sheet builder</p>
                        </div>
                        <a href="/" class="page-link">View Page</a>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                        <div>
                            <strong>Enhanced Dashboard</strong>
                            <p style="color: #64748b; font-size: 14px; margin-top: 4px;">Tool selection and analytics</p>
                        </div>
                        <a href="/dashboard" class="page-link">View Page</a>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                        <div>
                            <strong>Interest Search</strong>
                            <p style="color: #64748b; font-size: 14px; margin-top: 4px;">Advanced search interface</p>
                        </div>
                        <a href="/search" class="page-link">View Page</a>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                        <div>
                            <strong>Interest Suggestions</strong>
                            <p style="color: #64748b; font-size: 14px; margin-top: 4px;">AI-powered suggestions</p>
                        </div>
                        <a href="/suggestions" class="page-link">View Page</a>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                        <div>
                            <strong>Interest Pool</strong>
                            <p style="color: #64748b; font-size: 14px; margin-top: 4px;">Collection management</p>
                        </div>
                        <a href="/pool" class="page-link">View Page</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>