/**
 * Sidebar Component - Navigation sidebar for micro-tools
 * Shared across micro-tool pages
 */

export function Sidebar({ currentTool = '' }) {
  const toolItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      href: '/micro-tools',
      icon: `<rect x="3" y="3" width="7" height="7"></rect>
             <rect x="14" y="3" width="7" height="7"></rect>
             <rect x="14" y="14" width="7" height="7"></rect>
             <rect x="3" y="14" width="7" height="7"></rect>`
    },
    {
      id: 'interest-search',
      label: 'Interest Search',
      href: '/micro-tools/search',
      icon: `<circle cx="11" cy="11" r="8"></circle>
             <path d="m21 21-4.35-4.35"></path>`
    },
    {
      id: 'interest-suggestions',
      label: 'Interest Suggestions',
      href: '/micro-tools/suggestions',
      icon: `<path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>`
    },
    {
      id: 'taxonomy-browser',
      label: 'Taxonomy Browser',
      href: '/micro-tools/browse',
      icon: `<path d="M3 3v18h18"></path>
             <path d="m19 9-5 5-4-4-3 3"></path>`
    },
    {
      id: 'interest-pool',
      label: 'Interest Pool',
      href: '/micro-tools/pool',
      icon: `<path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>`
    }
  ];

  return `
    <aside class="sidebar lg:block sm:hidden">
      <h3 class="sidebar-title">Micro-Tools</h3>
      <nav class="sidebar-menu">
        ${toolItems.map(item => `
          <a href="${item.href}" 
             class="sidebar-item ${currentTool === item.id ? 'active' : ''}"
             data-tool="${item.id}">
            <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              ${item.icon}
            </svg>
            ${item.label}
          </a>
        `).join('')}
      </nav>
    </aside>
  `;
}

// Initialize sidebar interactions
export function initSidebar() {
  // Add any sidebar-specific interactions here
  const sidebarItems = document.querySelectorAll('.sidebar-item');
  
  sidebarItems.forEach(item => {
    item.addEventListener('click', (e) => {
      // Remove active class from all items
      sidebarItems.forEach(i => i.classList.remove('active'));
      // Add active class to clicked item
      e.currentTarget.classList.add('active');
    });
  });
}

export default Sidebar;