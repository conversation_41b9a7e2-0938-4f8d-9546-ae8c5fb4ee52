/**
 * Enhanced Dashboard Page - Matches enhanced-targetwise-dashboard.html
 */

export function DashboardPage() {
  return `
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <a href="/" class="logo">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            TargetWise
          </a>

          <nav class="nav-menu">
            <a href="/" class="nav-link">Home</a>
            <a href="/dashboard" class="nav-link active">Micro-Tools</a>
            <a href="/pricing" class="nav-link">Pricing</a>
            <a href="/docs" class="nav-link">Docs</a>
          </nav>

          <div class="header-actions">
            <a href="/admin" class="btn btn-outline">Admin</a>
            <a href="/create" class="btn btn-primary">Create Sheet</a>
            <button class="theme-toggle">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="5"></circle>
                <line x1="12" y1="1" x2="12" y2="3"></line>
                <line x1="12" y1="21" x2="12" y2="23"></line>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                <line x1="1" y1="12" x2="3" y2="12"></line>
                <line x1="21" y1="12" x2="23" y2="12"></line>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
              </svg>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Container -->
      <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
          <div class="sidebar-title">Micro-Tools</div>
          <nav class="sidebar-menu">
            <a href="/search" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
              Interest Search
            </a>

            <a href="/suggestions" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 11l3 3L22 4"></path>
                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
              </svg>
              Interest Suggestions
            </a>

            <a href="/taxonomy" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
              Taxonomy Browser
            </a>

            <a href="/pool" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              Interest Pool
            </a>
          </nav>
        </aside>

        <!-- Content -->
        <div class="content">
          <!-- Welcome Section -->
          <section class="welcome-section">
            <div class="welcome-content">
              <h1 class="welcome-title">Welcome to TargetWise</h1>
              <p class="welcome-subtitle">Access powerful micro-tools to build precise Facebook ad targeting strategies using the Meta Marketing API</p>
              <div class="welcome-actions">
                <a href="/search" class="btn btn-white">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="M21 21l-4.35-4.35"></path>
                  </svg>
                  Start Searching
                </a>
                <a href="/docs" class="btn btn-ghost">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  View Documentation
                </a>
              </div>
            </div>

            <!-- API Counter -->
            <div class="api-counter">
              <span>API Calls Today</span>
              <div class="api-counter-badge">247</div>
            </div>

            <!-- Background Circles -->
            <div class="circle-pattern circle-1"></div>
            <div class="circle-pattern circle-2"></div>
            <div class="circle-pattern circle-3"></div>
          </section>

          <!-- Tools Section -->
          <section class="tools-section">
            <div class="section-header">
              <h2 class="section-title">Micro-Tools</h2>
              <a href="/docs" class="view-all-link">
                View Documentation
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </a>
            </div>

            <div class="tools-grid">
              <a href="/search" class="tool-card">
                <div class="tool-icon">🔍</div>
                <h3 class="tool-title">Interest Search</h3>
                <p class="tool-description">Search for specific interests using keywords and get detailed audience data including size and demographics.</p>
                <div class="tool-action">
                  Start Searching
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </div>
              </a>

              <a href="/suggestions" class="tool-card">
                <div class="tool-icon">💡</div>
                <h3 class="tool-title">Interest Suggestions</h3>
                <p class="tool-description">Get AI-powered interest suggestions based on your seed interests to expand your targeting reach.</p>
                <div class="tool-action">
                  Get Suggestions
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </div>
              </a>

              <a href="/taxonomy" class="tool-card">
                <div class="tool-icon">🌳</div>
                <h3 class="tool-title">Taxonomy Browser</h3>
                <p class="tool-description">Browse Facebook's interest taxonomy to discover new targeting opportunities in specific categories.</p>
                <div class="tool-action">
                  Browse Categories
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </div>
              </a>

              <a href="/pool" class="tool-card">
                <div class="tool-icon">📋</div>
                <h3 class="tool-title">Interest Pool</h3>
                <p class="tool-description">Manage and organize your saved interests into pools for easy access and targeting sheet creation.</p>
                <div class="tool-action">
                  Manage Pools
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </div>
              </a>
            </div>
          </section>

        </div>
      </div>

      <!-- Floating Action Button -->
      <div class="fab">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M12 5v14"></path>
          <path d="M5 12h14"></path>
        </svg>
      </div>
  `;
}

export function initDashboardPage() {
  // Handle sidebar navigation
  const sidebarItems = document.querySelectorAll('.sidebar-item');
  sidebarItems.forEach(item => {
    item.addEventListener('click', (e) => {
      // Update active states
      sidebarItems.forEach(i => i.classList.remove('active'));
      item.classList.add('active');
    });
  });

  // Handle tool card clicks
  const toolCards = document.querySelectorAll('.tool-card');
  toolCards.forEach(card => {
    card.addEventListener('click', (e) => {
      e.preventDefault();
      const href = card.getAttribute('href');
      if (href) {
        window.location.href = href;
      }
    });
  });

  // Handle FAB click
  const fab = document.querySelector('.fab');
  if (fab) {
    fab.addEventListener('click', () => {
      // Show quick actions menu or navigate to create page
      window.location.href = '/';
    });
  }

  // Handle theme toggle
  const themeToggle = document.querySelector('.theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      document.body.classList.toggle('dark-theme');
    });
  }
}