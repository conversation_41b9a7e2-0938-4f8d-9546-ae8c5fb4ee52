/**
 * Enhanced Dashboard Page - Matches enhanced-targetwise-dashboard.html
 */

export function DashboardPage() {
  return `
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <a href="/" class="logo">
            <svg viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="14" stroke="currentColor" stroke-width="2" fill="none"/>
              <circle cx="16" cy="16" r="8" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.3"/>
              <circle cx="16" cy="16" r="4" fill="currentColor"/>
            </svg>
            TargetWise
          </a>
          
          <nav class="nav-menu">
            <a href="/" class="nav-link">Home</a>
            <a href="/dashboard" class="nav-link active">Dashboard</a>
            <a href="/search" class="nav-link">Interest Search</a>
            <a href="/suggestions" class="nav-link">Suggestions</a>
            <a href="/pool" class="nav-link">Interest Pool</a>
          </nav>
          
          <div class="header-actions">
            <button class="theme-toggle" aria-label="Toggle theme">
              <svg class="theme-icon-light" width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"/>
              </svg>
            </button>
            
            <div class="user-menu">
              <button class="user-avatar">
                <img src="https://ui-avatars.com/api/?name=User&background=2563eb&color=fff" alt="User">
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Dashboard Layout -->
      <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
          <div class="sidebar-header">
            <h2>Dashboard</h2>
          </div>
          
          <nav class="sidebar-nav">
            <a href="#overview" class="sidebar-link active">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
              Overview
            </a>
            
            <a href="#analytics" class="sidebar-link">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
              </svg>
              Analytics
            </a>
            
            <a href="#sheets" class="sidebar-link">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"/>
              </svg>
              Targeting Sheets
            </a>
            
            <a href="#interests" class="sidebar-link">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H4a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2 1 1 0 100-2 2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V7a2 2 0 012-2z" clip-rule="evenodd"/>
              </svg>
              Saved Interests
            </a>
            
            <a href="#settings" class="sidebar-link">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
              </svg>
              Settings
            </a>
          </nav>

          <div class="sidebar-footer">
            <div class="quota-meter">
              <h4>API Quota</h4>
              <div class="quota-bar">
                <div class="quota-fill" style="width: 35%"></div>
              </div>
              <p class="quota-text">350 / 1000 requests</p>
            </div>
          </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
          <!-- Overview Section -->
          <section id="overview" class="dashboard-section">
            <div class="section-header">
              <h1>Welcome back!</h1>
              <p class="section-subtitle">Here's what's happening with your targeting campaigns</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#2563eb" stroke-width="2">
                    <path d="M9 11l3 3L22 4"/>
                    <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"/>
                  </svg>
                </div>
                <div class="stat-content">
                  <h3 class="stat-value">24</h3>
                  <p class="stat-label">Active Sheets</p>
                  <span class="stat-change positive">+12% from last month</span>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 6v6l4 2"/>
                  </svg>
                </div>
                <div class="stat-content">
                  <h3 class="stat-value">3.2M</h3>
                  <p class="stat-label">Total Reach</p>
                  <span class="stat-change positive">+8% from last month</span>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <div class="stat-content">
                  <h3 class="stat-value">456</h3>
                  <p class="stat-label">Saved Interests</p>
                  <span class="stat-change">No change</span>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#8b5cf6" stroke-width="2">
                    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                  </svg>
                </div>
                <div class="stat-content">
                  <h3 class="stat-value">89%</h3>
                  <p class="stat-label">Success Rate</p>
                  <span class="stat-change positive">+5% from last month</span>
                </div>
              </div>
            </div>

            <!-- Recent Activity -->
            <div class="activity-section">
              <h2 class="section-title">Recent Activity</h2>
              <div class="activity-list">
                <div class="activity-item">
                  <div class="activity-icon success">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                      <path d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"/>
                    </svg>
                  </div>
                  <div class="activity-content">
                    <p class="activity-text">Generated targeting sheet for <strong>Fashion Enthusiasts</strong></p>
                    <span class="activity-time">2 hours ago</span>
                  </div>
                </div>

                <div class="activity-item">
                  <div class="activity-icon info">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                      <path d="M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
                      <path fill-rule="evenodd" d="M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z"/>
                    </svg>
                  </div>
                  <div class="activity-content">
                    <p class="activity-text">Added 12 new interests to <strong>Tech Savvy</strong> pool</p>
                    <span class="activity-time">5 hours ago</span>
                  </div>
                </div>

                <div class="activity-item">
                  <div class="activity-icon warning">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                      <path d="M8.982 1.566a1.13 1.13 0 00-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 01-1.1 0L7.1 5.995A.905.905 0 018 5zm.002 6a1 1 0 100 2 1 1 0 000-2z"/>
                    </svg>
                  </div>
                  <div class="activity-content">
                    <p class="activity-text">API quota running low - 150 requests remaining</p>
                    <span class="activity-time">1 day ago</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Analytics Section -->
          <section id="analytics" class="dashboard-section" style="display: none;">
            <div class="section-header">
              <h1>Analytics</h1>
              <div class="date-picker">
                <button class="date-range-btn">Last 30 days</button>
              </div>
            </div>

            <div class="charts-grid">
              <div class="chart-card">
                <h3 class="chart-title">Audience Growth</h3>
                <div class="chart-placeholder">
                  <canvas id="audience-chart"></canvas>
                </div>
              </div>

              <div class="chart-card">
                <h3 class="chart-title">Interest Categories</h3>
                <div class="chart-placeholder">
                  <canvas id="categories-chart"></canvas>
                </div>
              </div>
            </div>
          </section>

          <!-- Targeting Sheets Section -->
          <section id="sheets" class="dashboard-section" style="display: none;">
            <div class="section-header">
              <h1>Targeting Sheets</h1>
              <button class="btn-primary">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                </svg>
                Create New Sheet
              </button>
            </div>

            <div class="sheets-table">
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Created</th>
                    <th>Interests</th>
                    <th>Reach</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>Fashion Enthusiasts Q1</strong></td>
                    <td>Dec 15, 2023</td>
                    <td>156</td>
                    <td>2.4M</td>
                    <td><span class="badge badge-success">Active</span></td>
                    <td>
                      <div class="table-actions">
                        <button class="btn-icon" title="Download">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 2a.5.5 0 01.5.5v7.793l2.146-2.147a.5.5 0 01.708.708l-3 3a.5.5 0 01-.708 0l-3-3a.5.5 0 11.708-.708L7.5 10.293V2.5A.5.5 0 018 2z"/>
                            <path d="M3 12.5a.5.5 0 01.5-.5h9a.5.5 0 010 1h-9a.5.5 0 01-.5-.5z"/>
                          </svg>
                        </button>
                        <button class="btn-icon" title="Edit">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M12.146.146a.5.5 0 01.708 0l3 3a.5.5 0 010 .708l-10 10a.5.5 0 01-.168.11l-5 2a.5.5 0 01-.65-.65l2-5a.5.5 0 01.11-.168l10-10zM11.207 2.5L13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 01.5.5v.5h.5a.5.5 0 01.5.5v.5h.293l6.5-6.5zm-9.761 5.175l-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 015 12.5V12h-.5a.5.5 0 01-.5-.5V11h-.5a.5.5 0 01-.468-.325z"/>
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Tech Savvy Millennials</strong></td>
                    <td>Dec 10, 2023</td>
                    <td>203</td>
                    <td>1.8M</td>
                    <td><span class="badge badge-success">Active</span></td>
                    <td>
                      <div class="table-actions">
                        <button class="btn-icon" title="Download">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 2a.5.5 0 01.5.5v7.793l2.146-2.147a.5.5 0 01.708.708l-3 3a.5.5 0 01-.708 0l-3-3a.5.5 0 11.708-.708L7.5 10.293V2.5A.5.5 0 018 2z"/>
                            <path d="M3 12.5a.5.5 0 01.5-.5h9a.5.5 0 010 1h-9a.5.5 0 01-.5-.5z"/>
                          </svg>
                        </button>
                        <button class="btn-icon" title="Edit">
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M12.146.146a.5.5 0 01.708 0l3 3a.5.5 0 010 .708l-10 10a.5.5 0 01-.168.11l-5 2a.5.5 0 01-.65-.65l2-5a.5.5 0 01.11-.168l10-10zM11.207 2.5L13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 01.5.5v.5h.5a.5.5 0 01.5.5v.5h.293l6.5-6.5zm-9.761 5.175l-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 015 12.5V12h-.5a.5.5 0 01-.5-.5V11h-.5a.5.5 0 01-.468-.325z"/>
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>
        </main>
      </div>

      <!-- API Counter -->
      <div class="api-counter">
        <span class="api-label">API Calls</span>
        <span class="api-value">12,458</span>
      </div>

      <!-- Floating Action Button -->
      <button class="fab" title="Quick Actions">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0C5.383 0 0 5.383 0 12s5.383 12 12 12 12-5.383 12-12S18.617 0 12 0zm5 13h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
        </svg>
      </button>
    </div>
  `;
}

export function initDashboardPage() {
  // Sidebar navigation
  const sidebarLinks = document.querySelectorAll('.sidebar-link');
  const sections = document.querySelectorAll('.dashboard-section');
  
  sidebarLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      
      // Update active states
      sidebarLinks.forEach(l => l.classList.remove('active'));
      link.classList.add('active');
      
      // Show corresponding section
      const targetId = link.getAttribute('href').substring(1);
      sections.forEach(section => {
        section.style.display = section.id === targetId ? 'block' : 'none';
      });
    });
  });
  
  // Initialize charts (placeholder)
  initCharts();
  
  // Handle table actions
  const tableActions = document.querySelectorAll('.btn-icon');
  tableActions.forEach(btn => {
    btn.addEventListener('click', (e) => {
      const action = btn.getAttribute('title');
      console.log(`${action} action triggered`);
    });
  });
}

function initCharts() {
  // Placeholder for chart initialization
  // In a real app, you would use Chart.js or similar
  console.log('Charts initialized');
}