/**
 * Enhanced Dashboard Page - Matches enhanced-targetwise-dashboard.html
 */

export function DashboardPage() {
  return `
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <a href="/" class="logo">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
            TargetWise
          </a>

          <nav class="nav-menu">
            <a href="/" class="nav-link">Home</a>
            <a href="/dashboard" class="nav-link active">Micro-Tools</a>
            <a href="/docs" class="nav-link">Documentation</a>
            <a href="/pricing" class="nav-link">Pricing</a>
            <a href="/admin" class="nav-link">Admin</a>
          </nav>

          <div class="header-actions">
            <button class="theme-toggle">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="5"></circle>
                <line x1="12" y1="1" x2="12" y2="3"></line>
                <line x1="12" y1="21" x2="12" y2="23"></line>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                <line x1="1" y1="12" x2="3" y2="12"></line>
                <line x1="21" y1="12" x2="23" y2="12"></line>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
              </svg>
            </button>
            <a href="/login" class="btn btn-outline">Log In</a>
            <a href="/signup" class="btn btn-primary">Sign Up Free</a>
          </div>
        </div>
      </header>

      <!-- Main Container -->
      <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
          <h3 class="sidebar-title">Micro-Tools</h3>
          <nav class="sidebar-menu">
            <a href="/dashboard" class="sidebar-item active">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg>
              Dashboard
            </a>
            <a href="/search" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              Interest Search
            </a>
            <a href="/suggestions" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
              </svg>
              Interest Suggestions
            </a>
            <a href="/taxonomy" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3v18h18"></path>
                <path d="m19 9-5 5-4-4-3 3"></path>
              </svg>
              Taxonomy Browser
            </a>
            <a href="/pool" class="sidebar-item">
              <svg class="sidebar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"></path>
              </svg>
              Interest Pool
            </a>
          </nav>
        </aside>

        <!-- Content -->
        <div class="content">
          <!-- Welcome Section -->
          <section class="welcome-section">
            <div class="api-counter">
              API Calls: <span class="api-counter-badge">200</span>
            </div>

            <div class="welcome-content">
              <h1 class="welcome-title">Welcome to TargetWise Micro-Tools</h1>
              <p class="welcome-subtitle">Powerful utilities to enhance your Facebook ad targeting research and optimization workflow.</p>

              <div class="welcome-actions">
                <button class="btn btn-white">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                  Watch Tutorial
                </button>
                <button class="btn btn-ghost">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                  </svg>
                  Get Help
                </button>
              </div>
            </div>

            <!-- Decorative circles -->
            <div class="circle-pattern circle-1"></div>
            <div class="circle-pattern circle-2"></div>
            <div class="circle-pattern circle-3"></div>
          </section>

          <!-- Available Tools -->
          <section class="tools-section">
            <div class="section-header">
              <h2 class="section-title">Available Tools</h2>
              <a href="#" class="view-all-link">
                View All Tools
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </a>
            </div>

            <div class="tools-grid">
              <!-- Interest Search -->
              <div class="tool-card">
                <div class="tool-icon">🔍</div>
                <h3 class="tool-title">Interest Search</h3>
                <p class="tool-description">Search for specific interests and audience segments for your Facebook ads.</p>
                <a href="/search" class="tool-action">
                  Open Tool
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </a>
              </div>

              <!-- Interest Suggestions -->
              <div class="tool-card">
                <div class="tool-icon">💡</div>
                <h3 class="tool-title">Interest Suggestions</h3>
                <p class="tool-description">Get AI-powered interest suggestions based on your products and audience.</p>
                <a href="/suggestions" class="tool-action">
                  Open Tool
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </a>
              </div>

              <!-- Taxonomy Browser -->
              <div class="tool-card">
                <div class="tool-icon">🌳</div>
                <h3 class="tool-title">Taxonomy Browser</h3>
                <p class="tool-description">Explore Facebook's interest taxonomy and discover related interest categories.</p>
                <a href="/taxonomy" class="tool-action">
                  Open Tool
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </a>
              </div>

              <!-- Interest Pool -->
              <div class="tool-card">
                <div class="tool-icon">📁</div>
                <h3 class="tool-title">Interest Pool</h3>
                <p class="tool-description">Manage and export your collection of interests for use in Facebook ad campaigns.</p>
                <a href="/pool" class="tool-action">
                  Open Tool
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>
          </section>

        </div>
      </div>

      <!-- Floating Action Button -->
      <button class="fab">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </button>
  `;
}

export function initDashboardPage() {
  // Handle sidebar navigation
  const sidebarItems = document.querySelectorAll('.sidebar-item');
  sidebarItems.forEach(item => {
    item.addEventListener('click', (e) => {
      // Update active states
      sidebarItems.forEach(i => i.classList.remove('active'));
      item.classList.add('active');
    });
  });

  // Handle tool card clicks
  const toolCards = document.querySelectorAll('.tool-card');
  toolCards.forEach(card => {
    card.addEventListener('click', (e) => {
      e.preventDefault();
      const href = card.getAttribute('href');
      if (href) {
        window.location.href = href;
      }
    });
  });

  // Handle FAB click
  const fab = document.querySelector('.fab');
  if (fab) {
    fab.addEventListener('click', () => {
      // Show quick actions menu or navigate to create page
      window.location.href = '/';
    });
  }

  // Handle theme toggle
  const themeToggle = document.querySelector('.theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      document.body.classList.toggle('dark-theme');
    });
  }
}